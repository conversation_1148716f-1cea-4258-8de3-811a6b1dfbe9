/**
 * Customer Server Model - Enhanced HVAC CRM Implementation
 * Comprehensive customer management with HVAC-specific functionality
 */

import { prisma } from '~/db.server';
import type { Customer, CustomerPriority, Prisma } from '@prisma/client';

export type { Customer, CustomerPriority };

export interface CustomerWithRelations extends Customer {
  devices?: Array<{
    id: string;
    name: string;
    model?: string;
    type?: string;
    nextMaintenanceDate?: Date;
  }>;
  serviceOrders?: Array<{
    id: string;
    title: string;
    status: string;
    scheduledDate?: Date;
  }>;
  opportunities?: Array<{
    id: string;
    name: string;
    stage: string;
    value: number;
  }>;
  _count?: {
    devices: number;
    serviceOrders: number;
    opportunities: number;
    invoices: number;
  };
}

export interface CreateCustomerData {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  postalCode?: string;
  country?: string;
  notes?: string;
  preferredTechnician?: string;
  contractType?: string;
  priority?: CustomerPriority;
  userId: string;
}

export interface UpdateCustomerData {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  postalCode?: string;
  country?: string;
  notes?: string;
  preferredTechnician?: string;
  contractType?: string;
  priority?: CustomerPriority;
}

export interface CustomerSearchFilters {
  search?: string;
  priority?: CustomerPriority;
  city?: string;
  contractType?: string;
  hasPortalAccess?: boolean;
}

/**
 * Get a customer by ID with full relations
 */
export async function getCustomer(id: string, userId: string): Promise<CustomerWithRelations | null> {
  try {
    return await prisma.customer.findFirst({
      where: {
        id,
        userId, // Ensure user can only access their customers
      },
      include: {
        devices: {
          select: {
            id: true,
            name: true,
            model: true,
            type: true,
            nextMaintenanceDate: true,
          },
          orderBy: { name: 'asc' },
        },
        serviceOrders: {
          select: {
            id: true,
            title: true,
            status: true,
            scheduledDate: true,
          },
          orderBy: { createdAt: 'desc' },
          take: 5, // Latest 5 service orders
        },
        opportunities: {
          select: {
            id: true,
            name: true,
            stage: true,
            value: true,
          },
          orderBy: { createdAt: 'desc' },
          take: 3, // Latest 3 opportunities
        },
        _count: {
          select: {
            devices: true,
            serviceOrders: true,
            opportunities: true,
            invoices: true,
          },
        },
      },
    });
  } catch (error) {
    console.error('Error getting customer:', error);
    return null;
  }
}

/**
 * Get all customers for a user with search and filtering
 */
export async function getCustomers(
  userId: string,
  filters: CustomerSearchFilters = {},
  page: number = 1,
  limit: number = 20
): Promise<{ customers: CustomerWithRelations[]; total: number }> {
  try {
    const where: Prisma.CustomerWhereInput = {
      userId,
      ...(filters.search && {
        OR: [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { email: { contains: filters.search, mode: 'insensitive' } },
          { phone: { contains: filters.search, mode: 'insensitive' } },
          { address: { contains: filters.search, mode: 'insensitive' } },
        ],
      }),
      ...(filters.priority && { priority: filters.priority }),
      ...(filters.city && { city: { contains: filters.city, mode: 'insensitive' } }),
      ...(filters.contractType && { contractType: { contains: filters.contractType, mode: 'insensitive' } }),
      ...(filters.hasPortalAccess !== undefined && { hasPortalAccess: filters.hasPortalAccess }),
    };

    const [customers, total] = await Promise.all([
      prisma.customer.findMany({
        where,
        include: {
          _count: {
            select: {
              devices: true,
              serviceOrders: true,
              opportunities: true,
              invoices: true,
            },
          },
        },
        orderBy: [
          { priority: 'desc' }, // High priority first
          { name: 'asc' },
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.customer.count({ where }),
    ]);

    return { customers, total };
  } catch (error) {
    console.error('Error getting customers:', error);
    return { customers: [], total: 0 };
  }
}

/**
 * Create a new customer
 */
export async function createCustomer(data: CreateCustomerData): Promise<Customer | null> {
  try {
    return await prisma.customer.create({
      data: {
        ...data,
        country: data.country || 'Polska', // Default to Poland
      },
    });
  } catch (error) {
    console.error('Error creating customer:', error);
    return null;
  }
}

/**
 * Update a customer
 */
export async function updateCustomer(
  id: string,
  userId: string,
  data: UpdateCustomerData
): Promise<Customer | null> {
  try {
    return await prisma.customer.update({
      where: {
        id,
        userId, // Ensure user can only update their customers
      },
      data,
    });
  } catch (error) {
    console.error('Error updating customer:', error);
    return null;
  }
}

/**
 * Delete a customer (soft delete by setting inactive)
 */
export async function deleteCustomer(id: string, userId: string): Promise<boolean> {
  try {
    // Hard delete for now, but check ownership
    await prisma.customer.delete({
      where: {
        id,
        userId,
      },
    });
    return true;
  } catch (error) {
    console.error('Error deleting customer:', error);
    return false;
  }
}

/**
 * Get customer statistics for dashboard
 */
export async function getCustomerStats(userId: string) {
  try {
    const [total, byPriority, recentlyAdded] = await Promise.all([
      prisma.customer.count({ where: { userId } }),
      prisma.customer.groupBy({
        by: ['priority'],
        where: { userId },
        _count: { priority: true },
      }),
      prisma.customer.count({
        where: {
          userId,
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
    ]);

    return {
      total,
      byPriority: byPriority.reduce((acc, item) => {
        acc[item.priority] = item._count.priority;
        return acc;
      }, {} as Record<string, number>),
      recentlyAdded,
    };
  } catch (error) {
    console.error('Error getting customer stats:', error);
    return {
      total: 0,
      byPriority: {},
      recentlyAdded: 0,
    };
  }
}

/**
 * Get customer by email
 */
export async function getCustomerByEmail(email: string, userId: string): Promise<Customer | null> {
  try {
    return await prisma.customer.findFirst({
      where: {
        email,
        userId,
      },
    });
  } catch (error) {
    console.error('Error getting customer by email:', error);
    return null;
  }
}

/**
 * Get recent customers
 */
export async function getRecentCustomers(userId: string, limit: number = 10): Promise<Customer[]> {
  try {
    return await prisma.customer.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
    });
  } catch (error) {
    console.error('Error getting recent customers:', error);
    return [];
  }
}

/**
 * Get customers with upcoming maintenance
 */
export async function getCustomersWithUpcomingMaintenance(userId: string): Promise<CustomerWithRelations[]> {
  try {
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    return await prisma.customer.findMany({
      where: {
        userId,
        devices: {
          some: {
            nextMaintenanceDate: {
              lte: thirtyDaysFromNow,
            },
          },
        },
      },
      include: {
        devices: {
          where: {
            nextMaintenanceDate: {
              lte: thirtyDaysFromNow,
            },
          },
          select: {
            id: true,
            name: true,
            model: true,
            type: true,
            nextMaintenanceDate: true,
          },
        },
        _count: {
          select: {
            devices: true,
            serviceOrders: true,
            opportunities: true,
            invoices: true,
          },
        },
      },
      orderBy: { name: 'asc' },
    });
  } catch (error) {
    console.error('Error getting customers with upcoming maintenance:', error);
    return [];
  }
}
