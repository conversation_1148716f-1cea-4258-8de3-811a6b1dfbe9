/**
 * 📋 PROTOCOL SERVICE
 * 
 * Serwis do zarządzania protokołami serwisowymi z podpisem cyfrowym
 * Integracja z GoSpine backend API
 */

import { goBackendBridge } from './gobackend-bridge.service';

export interface ServiceProtocolData {
  serviceOrderId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  serviceAddress: string;
  equipmentDetails: {
    brand: string;
    model: string;
    serialNumber: string;
    installationDate?: string;
  };
  serviceDetails: {
    serviceType: 'installation' | 'repair' | 'maintenance' | 'inspection';
    description: string;
    workPerformed: string;
    partsUsed: Array<{
      name: string;
      quantity: number;
      partNumber?: string;
      cost?: number;
    }>;
    laborHours: number;
    recommendations: string;
  };
  technicianDetails: {
    name: string;
    certification: string;
    employeeId: string;
  };
  qualityChecks: Array<{
    item: string;
    status: 'pass' | 'fail' | 'na';
    notes?: string;
  }>;
  photos: Array<{
    id: string;
    url: string;
    description: string;
    timestamp: string;
  }>;
}

export interface SignatureData {
  technicianSignature: string;
  customerSignature: string;
  signedAt: string;
  ipAddress?: string;
  deviceInfo?: string;
}

export interface ServiceProtocol {
  id: string;
  serviceOrderId: string;
  protocolNumber: string;
  status: 'draft' | 'completed' | 'sent';
  data: ServiceProtocolData;
  signatures?: SignatureData;
  pdfUrl?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export class ProtocolService {
  
  /**
   * Pobierz protokół dla zlecenia serwisowego
   */
  static async getProtocolByServiceOrderId(serviceOrderId: string): Promise<ServiceProtocol | null> {
    try {
      const response = await goBackendBridge.get(`/api/protocols/service-order/${serviceOrderId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching protocol:', error);
      return null;
    }
  }

  /**
   * Pobierz protokół po ID
   */
  static async getProtocolById(protocolId: string): Promise<ServiceProtocol | null> {
    try {
      const response = await goBackendBridge.get(`/api/protocols/${protocolId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching protocol by ID:', error);
      return null;
    }
  }

  /**
   * Zapisz roboczą wersję protokołu
   */
  static async saveProtocolDraft(
    serviceOrderId: string, 
    protocolData: ServiceProtocolData, 
    userId: string
  ): Promise<ServiceProtocol> {
    try {
      const response = await goBackendBridge.post('/api/protocols/draft', {
        serviceOrderId,
        data: protocolData,
        createdBy: userId
      });
      
      return response.data;
    } catch (error) {
      console.error('Error saving protocol draft:', error);
      throw new Error('Nie udało się zapisać roboczej wersji protokołu');
    }
  }

  /**
   * Finalizuj protokół z podpisami
   */
  static async completeProtocol(
    serviceOrderId: string,
    protocolData: ServiceProtocolData,
    signatures: SignatureData,
    userId: string
  ): Promise<ServiceProtocol> {
    try {
      const response = await goBackendBridge.post('/api/protocols/complete', {
        serviceOrderId,
        data: protocolData,
        signatures,
        createdBy: userId
      });
      
      return response.data;
    } catch (error) {
      console.error('Error completing protocol:', error);
      throw new Error('Nie udało się sfinalizować protokołu');
    }
  }

  /**
   * Wygeneruj PDF protokołu
   */
  static async generateProtocolPDF(protocolId: string): Promise<string> {
    try {
      const response = await goBackendBridge.post(`/api/protocols/${protocolId}/generate-pdf`);
      return response.data.pdfUrl;
    } catch (error) {
      console.error('Error generating protocol PDF:', error);
      throw new Error('Nie udało się wygenerować PDF protokołu');
    }
  }

  /**
   * Wyślij protokół do klienta
   */
  static async sendProtocolToCustomer(protocolId: string, customerEmail?: string): Promise<boolean> {
    try {
      await goBackendBridge.post(`/api/protocols/${protocolId}/send`, {
        customerEmail
      });
      return true;
    } catch (error) {
      console.error('Error sending protocol to customer:', error);
      throw new Error('Nie udało się wysłać protokołu do klienta');
    }
  }

  /**
   * Pobierz listę protokołów
   */
  static async getProtocols(filters?: {
    status?: string;
    dateFrom?: string;
    dateTo?: string;
    technicianId?: string;
    customerId?: string;
  }): Promise<ServiceProtocol[]> {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value) queryParams.append(key, value);
        });
      }
      
      const response = await goBackendBridge.get(`/api/protocols?${queryParams.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching protocols:', error);
      return [];
    }
  }

  /**
   * Usuń protokół (tylko robocze wersje)
   */
  static async deleteProtocol(protocolId: string): Promise<boolean> {
    try {
      await goBackendBridge.delete(`/api/protocols/${protocolId}`);
      return true;
    } catch (error) {
      console.error('Error deleting protocol:', error);
      throw new Error('Nie udało się usunąć protokołu');
    }
  }

  /**
   * Aktualizuj roboczą wersję protokołu
   */
  static async updateProtocolDraft(
    protocolId: string,
    protocolData: Partial<ServiceProtocolData>
  ): Promise<ServiceProtocol> {
    try {
      const response = await goBackendBridge.put(`/api/protocols/${protocolId}`, {
        data: protocolData
      });
      
      return response.data;
    } catch (error) {
      console.error('Error updating protocol draft:', error);
      throw new Error('Nie udało się zaktualizować protokołu');
    }
  }

  /**
   * Pobierz szablony protokołów
   */
  static async getProtocolTemplates(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    serviceType: string;
    template: Partial<ServiceProtocolData>;
  }>> {
    try {
      const response = await goBackendBridge.get('/api/protocols/templates');
      return response.data;
    } catch (error) {
      console.error('Error fetching protocol templates:', error);
      return [];
    }
  }

  /**
   * Zapisz szablon protokołu
   */
  static async saveProtocolTemplate(template: {
    name: string;
    description: string;
    serviceType: string;
    template: Partial<ServiceProtocolData>;
  }): Promise<boolean> {
    try {
      await goBackendBridge.post('/api/protocols/templates', template);
      return true;
    } catch (error) {
      console.error('Error saving protocol template:', error);
      throw new Error('Nie udało się zapisać szablonu protokołu');
    }
  }

  /**
   * Pobierz statystyki protokołów
   */
  static async getProtocolStatistics(period: 'week' | 'month' | 'quarter' | 'year' = 'month'): Promise<{
    totalProtocols: number;
    completedProtocols: number;
    draftProtocols: number;
    averageCompletionTime: number;
    customerSatisfactionAverage: number;
    protocolsByServiceType: Array<{
      serviceType: string;
      count: number;
    }>;
    protocolsByTechnician: Array<{
      technicianName: string;
      count: number;
      averageRating: number;
    }>;
  }> {
    try {
      const response = await goBackendBridge.get(`/api/protocols/statistics?period=${period}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching protocol statistics:', error);
      return {
        totalProtocols: 0,
        completedProtocols: 0,
        draftProtocols: 0,
        averageCompletionTime: 0,
        customerSatisfactionAverage: 0,
        protocolsByServiceType: [],
        protocolsByTechnician: []
      };
    }
  }

  /**
   * Waliduj dane protokołu
   */
  static validateProtocolData(data: ServiceProtocolData): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Walidacja danych klienta
    if (!data.customerName.trim()) {
      errors.push('Nazwa klienta jest wymagana');
    }
    if (!data.customerEmail.trim()) {
      errors.push('Email klienta jest wymagany');
    }
    if (!data.serviceAddress.trim()) {
      errors.push('Adres serwisu jest wymagany');
    }

    // Walidacja danych urządzenia
    if (!data.equipmentDetails.brand.trim()) {
      errors.push('Marka urządzenia jest wymagana');
    }
    if (!data.equipmentDetails.model.trim()) {
      errors.push('Model urządzenia jest wymagany');
    }

    // Walidacja danych serwisu
    if (!data.serviceDetails.workPerformed.trim()) {
      errors.push('Opis wykonanych prac jest wymagany');
    }
    if (data.serviceDetails.laborHours <= 0) {
      errors.push('Liczba godzin pracy musi być większa od 0');
    }

    // Walidacja danych technika
    if (!data.technicianDetails.name.trim()) {
      errors.push('Nazwa technika jest wymagana');
    }
    if (!data.technicianDetails.employeeId.trim()) {
      errors.push('ID pracownika jest wymagane');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Waliduj podpisy
   */
  static validateSignatures(signatures: SignatureData): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!signatures.technicianSignature || signatures.technicianSignature.length < 100) {
      errors.push('Podpis technika jest wymagany');
    }
    if (!signatures.customerSignature || signatures.customerSignature.length < 100) {
      errors.push('Podpis klienta jest wymagany');
    }
    if (!signatures.signedAt) {
      errors.push('Data podpisania jest wymagana');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Generuj numer protokołu
   */
  static generateProtocolNumber(serviceOrderId: string): string {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const timestamp = Date.now().toString().slice(-6);
    
    return `PROT-${year}${month}${day}-${serviceOrderId.slice(-4)}-${timestamp}`;
  }
}
