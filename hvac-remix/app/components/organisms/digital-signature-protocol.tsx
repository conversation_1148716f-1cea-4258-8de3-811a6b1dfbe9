/**
 * 🖊️ DIGITAL SIGNATURE PROTOCOL COMPONENT
 * 
 * Kompletny system protokołów serwisowych z podpisem cyfrowym
 * Inspirowany ServiceTool.pl - automatyczne generowanie protokołów z podpisem klienta
 */

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Badge } from "~/components/ui/badge";
import { Separator } from "~/components/ui/separator";
import { 
  FileText, 
  PenTool, 
  Download, 
  Send, 
  CheckCircle, 
  Camera,
  Trash2,
  Save,
  RefreshCw
} from "lucide-react";

interface ServiceProtocolData {
  serviceOrderId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  serviceAddress: string;
  equipmentDetails: {
    brand: string;
    model: string;
    serialNumber: string;
    installationDate?: string;
  };
  serviceDetails: {
    serviceType: 'installation' | 'repair' | 'maintenance' | 'inspection';
    description: string;
    workPerformed: string;
    partsUsed: Array<{
      name: string;
      quantity: number;
      partNumber?: string;
    }>;
    laborHours: number;
    recommendations: string;
  };
  technicianDetails: {
    name: string;
    certification: string;
    employeeId: string;
  };
  qualityChecks: Array<{
    item: string;
    status: 'pass' | 'fail' | 'na';
    notes?: string;
  }>;
  photos: Array<{
    id: string;
    url: string;
    description: string;
    timestamp: string;
  }>;
}

interface DigitalSignatureProtocolProps {
  serviceOrderId: string;
  initialData?: Partial<ServiceProtocolData>;
  onSave?: (data: ServiceProtocolData) => void;
  onComplete?: (data: ServiceProtocolData, signatures: SignatureData) => void;
}

interface SignatureData {
  technicianSignature: string;
  customerSignature: string;
  signedAt: string;
  ipAddress?: string;
  deviceInfo?: string;
}

export function DigitalSignatureProtocol({ 
  serviceOrderId, 
  initialData, 
  onSave, 
  onComplete 
}: DigitalSignatureProtocolProps) {
  const [protocolData, setProtocolData] = useState<ServiceProtocolData>({
    serviceOrderId,
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    serviceAddress: '',
    equipmentDetails: {
      brand: '',
      model: '',
      serialNumber: ''
    },
    serviceDetails: {
      serviceType: 'maintenance',
      description: '',
      workPerformed: '',
      partsUsed: [],
      laborHours: 0,
      recommendations: ''
    },
    technicianDetails: {
      name: '',
      certification: '',
      employeeId: ''
    },
    qualityChecks: [
      { item: 'Sprawdzenie działania systemu', status: 'na' },
      { item: 'Test bezpieczeństwa', status: 'na' },
      { item: 'Kontrola szczelności', status: 'na' },
      { item: 'Pomiar parametrów', status: 'na' },
      { item: 'Czyszczenie filtrów', status: 'na' }
    ],
    photos: [],
    ...initialData
  });

  const [currentStep, setCurrentStep] = useState<'details' | 'quality' | 'photos' | 'signatures'>('details');
  const [signatures, setSignatures] = useState<Partial<SignatureData>>({});
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  // Canvas refs for signatures
  const technicianCanvasRef = useRef<HTMLCanvasElement>(null);
  const customerCanvasRef = useRef<HTMLCanvasElement>(null);

  const steps = [
    { id: 'details', label: 'Szczegóły serwisu', icon: FileText },
    { id: 'quality', label: 'Kontrola jakości', icon: CheckCircle },
    { id: 'photos', label: 'Dokumentacja foto', icon: Camera },
    { id: 'signatures', label: 'Podpisy', icon: PenTool }
  ];

  // Initialize signature canvas
  const initializeCanvas = useCallback((canvas: HTMLCanvasElement) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.strokeStyle = '#000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    let isDrawing = false;
    let lastX = 0;
    let lastY = 0;

    const startDrawing = (e: MouseEvent | TouchEvent) => {
      isDrawing = true;
      const rect = canvas.getBoundingClientRect();
      const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
      const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
      lastX = clientX - rect.left;
      lastY = clientY - rect.top;
    };

    const draw = (e: MouseEvent | TouchEvent) => {
      if (!isDrawing) return;
      const rect = canvas.getBoundingClientRect();
      const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
      const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
      const currentX = clientX - rect.left;
      const currentY = clientY - rect.top;

      ctx.beginPath();
      ctx.moveTo(lastX, lastY);
      ctx.lineTo(currentX, currentY);
      ctx.stroke();

      lastX = currentX;
      lastY = currentY;
    };

    const stopDrawing = () => {
      isDrawing = false;
    };

    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('touchstart', startDrawing);
    canvas.addEventListener('touchmove', draw);
    canvas.addEventListener('touchend', stopDrawing);

    return () => {
      canvas.removeEventListener('mousedown', startDrawing);
      canvas.removeEventListener('mousemove', draw);
      canvas.removeEventListener('mouseup', stopDrawing);
      canvas.removeEventListener('touchstart', startDrawing);
      canvas.removeEventListener('touchmove', draw);
      canvas.removeEventListener('touchend', stopDrawing);
    };
  }, []);

  useEffect(() => {
    if (technicianCanvasRef.current) {
      initializeCanvas(technicianCanvasRef.current);
    }
    if (customerCanvasRef.current) {
      initializeCanvas(customerCanvasRef.current);
    }
  }, [initializeCanvas, currentStep]);

  const clearSignature = (type: 'technician' | 'customer') => {
    const canvas = type === 'technician' ? technicianCanvasRef.current : customerCanvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      ctx?.clearRect(0, 0, canvas.width, canvas.height);
    }
  };

  const captureSignature = (type: 'technician' | 'customer') => {
    const canvas = type === 'technician' ? technicianCanvasRef.current : customerCanvasRef.current;
    if (canvas) {
      return canvas.toDataURL('image/png');
    }
    return '';
  };

  const handleStepChange = (step: string) => {
    setCurrentStep(step as any);
  };

  const handleSave = () => {
    if (onSave) {
      onSave(protocolData);
    }
  };

  const handleComplete = async () => {
    const technicianSig = captureSignature('technician');
    const customerSig = captureSignature('customer');

    if (!technicianSig || !customerSig) {
      alert('Proszę uzupełnić wszystkie podpisy');
      return;
    }

    const signatureData: SignatureData = {
      technicianSignature: technicianSig,
      customerSignature: customerSig,
      signedAt: new Date().toISOString(),
      ipAddress: await getClientIP(),
      deviceInfo: navigator.userAgent
    };

    setSignatures(signatureData);

    if (onComplete) {
      onComplete(protocolData, signatureData);
    }
  };

  const getClientIP = async (): Promise<string> => {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch {
      return 'unknown';
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'details':
        return <ServiceDetailsStep data={protocolData} onChange={setProtocolData} />;
      case 'quality':
        return <QualityCheckStep data={protocolData} onChange={setProtocolData} />;
      case 'photos':
        return <PhotoDocumentationStep data={protocolData} onChange={setProtocolData} />;
      case 'signatures':
        return (
          <SignatureStep
            technicianCanvasRef={technicianCanvasRef}
            customerCanvasRef={customerCanvasRef}
            onClearSignature={clearSignature}
            protocolData={protocolData}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-6 w-6" />
            Protokół Serwisowy - {serviceOrderId}
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Progress Steps */}
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-center">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <Button
                  variant={currentStep === step.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleStepChange(step.id)}
                  className="flex items-center gap-2"
                >
                  <step.icon className="h-4 w-4" />
                  {step.label}
                </Button>
                {index < steps.length - 1 && (
                  <div className="w-8 h-px bg-gray-300 mx-2" />
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      {renderStepContent()}

      {/* Action Buttons */}
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between">
            <Button variant="outline" onClick={handleSave}>
              <Save className="h-4 w-4 mr-2" />
              Zapisz roboczą wersję
            </Button>
            
            <div className="flex gap-2">
              {currentStep !== 'signatures' && (
                <Button 
                  onClick={() => {
                    const currentIndex = steps.findIndex(s => s.id === currentStep);
                    if (currentIndex < steps.length - 1) {
                      setCurrentStep(steps[currentIndex + 1].id as any);
                    }
                  }}
                >
                  Następny krok
                </Button>
              )}
              
              {currentStep === 'signatures' && (
                <Button onClick={handleComplete} className="bg-green-600 hover:bg-green-700">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Zakończ protokół
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Helper components for each step
function ServiceDetailsStep({ data, onChange }: { data: ServiceProtocolData, onChange: (data: ServiceProtocolData) => void }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Szczegóły serwisu</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Customer Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="customerName">Nazwa klienta</Label>
            <Input
              id="customerName"
              value={data.customerName}
              onChange={(e) => onChange({ ...data, customerName: e.target.value })}
            />
          </div>
          <div>
            <Label htmlFor="customerEmail">Email</Label>
            <Input
              id="customerEmail"
              type="email"
              value={data.customerEmail}
              onChange={(e) => onChange({ ...data, customerEmail: e.target.value })}
            />
          </div>
        </div>

        {/* Equipment Details */}
        <Separator />
        <h3 className="text-lg font-semibold">Szczegóły urządzenia</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="brand">Marka</Label>
            <Input
              id="brand"
              value={data.equipmentDetails.brand}
              onChange={(e) => onChange({
                ...data,
                equipmentDetails: { ...data.equipmentDetails, brand: e.target.value }
              })}
            />
          </div>
          <div>
            <Label htmlFor="model">Model</Label>
            <Input
              id="model"
              value={data.equipmentDetails.model}
              onChange={(e) => onChange({
                ...data,
                equipmentDetails: { ...data.equipmentDetails, model: e.target.value }
              })}
            />
          </div>
          <div>
            <Label htmlFor="serialNumber">Numer seryjny</Label>
            <Input
              id="serialNumber"
              value={data.equipmentDetails.serialNumber}
              onChange={(e) => onChange({
                ...data,
                equipmentDetails: { ...data.equipmentDetails, serialNumber: e.target.value }
              })}
            />
          </div>
        </div>

        {/* Service Details */}
        <Separator />
        <h3 className="text-lg font-semibold">Wykonane prace</h3>
        <div>
          <Label htmlFor="workPerformed">Opis wykonanych prac</Label>
          <Textarea
            id="workPerformed"
            value={data.serviceDetails.workPerformed}
            onChange={(e) => onChange({
              ...data,
              serviceDetails: { ...data.serviceDetails, workPerformed: e.target.value }
            })}
            rows={4}
          />
        </div>
      </CardContent>
    </Card>
  );
}

function QualityCheckStep({ data, onChange }: { data: ServiceProtocolData, onChange: (data: ServiceProtocolData) => void }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Kontrola jakości</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.qualityChecks.map((check, index) => (
            <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
              <span className="font-medium">{check.item}</span>
              <div className="flex gap-2">
                {(['pass', 'fail', 'na'] as const).map((status) => (
                  <Button
                    key={status}
                    variant={check.status === status ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      const newChecks = [...data.qualityChecks];
                      newChecks[index] = { ...check, status };
                      onChange({ ...data, qualityChecks: newChecks });
                    }}
                  >
                    {status === 'pass' ? '✓' : status === 'fail' ? '✗' : 'N/A'}
                  </Button>
                ))}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

function PhotoDocumentationStep({ data, onChange }: { data: ServiceProtocolData, onChange: (data: ServiceProtocolData) => void }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Dokumentacja fotograficzna</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {data.photos.map((photo) => (
            <div key={photo.id} className="border rounded-lg p-4">
              <img src={photo.url} alt={photo.description} className="w-full h-32 object-cover rounded" />
              <p className="mt-2 text-sm">{photo.description}</p>
            </div>
          ))}
          <Button variant="outline" className="h-32 border-dashed">
            <Camera className="h-8 w-8" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

function SignatureStep({ 
  technicianCanvasRef, 
  customerCanvasRef, 
  onClearSignature,
  protocolData 
}: { 
  technicianCanvasRef: React.RefObject<HTMLCanvasElement>;
  customerCanvasRef: React.RefObject<HTMLCanvasElement>;
  onClearSignature: (type: 'technician' | 'customer') => void;
  protocolData: ServiceProtocolData;
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Podpisy</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Technician Signature */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <Label>Podpis technika: {protocolData.technicianDetails.name}</Label>
            <Button variant="outline" size="sm" onClick={() => onClearSignature('technician')}>
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
          <canvas
            ref={technicianCanvasRef}
            width={400}
            height={150}
            className="border border-gray-300 rounded w-full"
            style={{ touchAction: 'none' }}
          />
        </div>

        {/* Customer Signature */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <Label>Podpis klienta: {protocolData.customerName}</Label>
            <Button variant="outline" size="sm" onClick={() => onClearSignature('customer')}>
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
          <canvas
            ref={customerCanvasRef}
            width={400}
            height={150}
            className="border border-gray-300 rounded w-full"
            style={{ touchAction: 'none' }}
          />
        </div>

        <div className="text-sm text-gray-600 p-4 bg-gray-50 rounded">
          <p><strong>Oświadczenie klienta:</strong></p>
          <p>Potwierdzam wykonanie prac serwisowych zgodnie z opisem powyżej. 
          Zostałem poinformowany o sposobie użytkowania urządzenia oraz zaleceniach serwisowych.</p>
        </div>
      </CardContent>
    </Card>
  );
}
