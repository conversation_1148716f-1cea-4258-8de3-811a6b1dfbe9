import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSearchParams, Link } from "@remix-run/react";
import * as React from "react";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  getCustomers,
  getCustomerStats,
  type CustomerWithRelations,
  type CustomerSearchFilters
} from "~/models/customer.server";
import { requireUserId } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const url = new URL(request.url);

  // Parse search parameters
  const search = url.searchParams.get('search') || '';
  const priority = url.searchParams.get('priority') || '';
  const city = url.searchParams.get('city') || '';
  const page = parseInt(url.searchParams.get('page') || '1');
  const limit = parseInt(url.searchParams.get('limit') || '20');

  const filters: CustomerSearchFilters = {
    ...(search && { search }),
    ...(priority && { priority: priority as any }),
    ...(city && { city }),
  };

  try {
    const [customersData, stats] = await Promise.all([
      getCustomers(userId, filters, page, limit),
      getCustomerStats(userId),
    ]);

    return json({
      customers: customersData.customers,
      total: customersData.total,
      stats,
      filters,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(customersData.total / limit),
      },
    });
  } catch (error) {
    console.error('Error loading customers:', error);
    return json({
      customers: [],
      total: 0,
      stats: { total: 0, byPriority: {}, recentlyAdded: 0 },
      filters,
      pagination: { page: 1, limit: 20, totalPages: 0 },
      error: 'Błąd podczas ładowania klientów',
    });
  }
}

// Polish translations
const translations = {
  customers: 'Klienci',
  search: 'Szukaj',
  addCustomer: 'Dodaj Klienta',
  name: 'Nazwa',
  email: 'Email',
  phone: 'Telefon',
  city: 'Miasto',
  priority: 'Priorytet',
  devices: 'Urządzenia',
  serviceOrders: 'Zlecenia Serwisowe',
  opportunities: 'Możliwości',
  actions: 'Akcje',
  edit: 'Edytuj',
  view: 'Zobacz',
  total: 'Łącznie',
  recentlyAdded: 'Dodano ostatnio',
  highPriority: 'Wysoki priorytet',
  mediumPriority: 'Średni priorytet',
  lowPriority: 'Niski priorytet',
  urgentPriority: 'Pilny',
  noCustomers: 'Brak klientów',
  loading: 'Ładowanie...',
};

const priorityColors = {
  URGENT: 'bg-red-100 text-red-800',
  HIGH: 'bg-orange-100 text-orange-800',
  MEDIUM: 'bg-yellow-100 text-yellow-800',
  LOW: 'bg-green-100 text-green-800',
};

const priorityLabels = {
  URGENT: translations.urgentPriority,
  HIGH: translations.highPriority,
  MEDIUM: translations.mediumPriority,
  LOW: translations.lowPriority,
};

export default function CustomersIndexPage() {
  const { customers, total, stats, filters, pagination, error } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(filters.search || "");

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const newParams = new URLSearchParams(searchParams);
    if (searchQuery) {
      newParams.set("search", searchQuery);
    } else {
      newParams.delete("search");
    }
    newParams.set("page", "1"); // Reset to first page on new search
    setSearchParams(newParams);
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("page", newPage.toString());
    setSearchParams(newParams);
  };

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">{translations.customers}</h1>
          <p className="text-gray-600">
            {translations.total}: {total} | {translations.recentlyAdded}: {stats.recentlyAdded}
          </p>
        </div>
        <div className="flex gap-2">
          <Link to="/customers/map">
            <Button variant="outline">
              🗺️ Mapa
            </Button>
          </Link>
          <Link to="/customers/new">
            <Button>{translations.addCustomer}</Button>
          </Link>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm font-medium text-gray-500">{translations.total}</h3>
            <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm font-medium text-gray-500">{translations.urgentPriority}</h3>
            <p className="text-2xl font-bold text-red-600">{stats.byPriority.URGENT || 0}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm font-medium text-gray-500">{translations.highPriority}</h3>
            <p className="text-2xl font-bold text-orange-600">{stats.byPriority.HIGH || 0}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm font-medium text-gray-500">{translations.recentlyAdded}</h3>
            <p className="text-2xl font-bold text-green-600">{stats.recentlyAdded}</p>
          </CardContent>
        </Card>
      </div>

      {/* Search form */}
      <form onSubmit={handleSearch} className="mb-6">
        <div className="flex gap-2">
          <div className="flex-1">
            <Input
              type="text"
              placeholder="Szukaj klientów..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button type="submit">{translations.search}</Button>
        </div>
      </form>

      {/* Error message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Customers list */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {customers.length > 0 ? (
          customers.map((customer) => (
            <CustomerCard key={customer.id} customer={customer} />
          ))
        ) : (
          <div className="col-span-full text-center py-8">
            <p className="text-gray-500">{translations.noCustomers}</p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
            >
              Poprzednia
            </Button>

            <div className="flex items-center gap-1">
              {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
                <Button
                  key={page}
                  variant={page === pagination.page ? "default" : "outline"}
                  onClick={() => handlePageChange(page)}
                  className="w-10"
                >
                  {page}
                </Button>
              ))}
            </div>

            <Button
              variant="outline"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.totalPages}
            >
              Następna
            </Button>
          </div>
        </div>
      )}

      {/* Summary */}
      <div className="mt-6 text-center text-gray-500">
        Wyświetlanie {customers.length} z {total} klientów
      </div>
    </div>
  );
}

function CustomerCard({ customer }: { customer: CustomerWithRelations }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-start">
          <Link to={`/customers/${customer.id}`} className="hover:underline">
            {customer.name}
          </Link>
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${priorityColors[customer.priority]}`}>
            {priorityLabels[customer.priority]}
          </span>
        </CardTitle>
        <CardDescription>
          {customer.email || "Brak email"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {customer.phone && (
            <div>
              <Label>{translations.phone}</Label>
              <p>{customer.phone}</p>
            </div>
          )}
          {customer.address && (
            <div>
              <Label>Adres</Label>
              <p>{customer.address}</p>
              {customer.city && customer.postalCode && (
                <p>
                  {customer.city}, {customer.postalCode}
                  {customer.country ? `, ${customer.country}` : ""}
                </p>
              )}
            </div>
          )}
          {customer._count && (
            <div className="flex justify-between text-sm text-gray-500 pt-2">
              <span>🔧 {customer._count.devices}</span>
              <span>📋 {customer._count.serviceOrders}</span>
              <span>💼 {customer._count.opportunities}</span>
              <span>💰 {customer._count.invoices}</span>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link to={`/customers/${customer.id}`}>
          <Button variant="outline">{translations.view}</Button>
        </Link>
        <Link to={`/customers/${customer.id}/edit`}>
          <Button variant="outline">{translations.edit}</Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
