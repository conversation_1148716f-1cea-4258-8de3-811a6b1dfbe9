/**
 * 📋 SERVICE PROTOCOL ROUTE
 * 
 * Route dla tworzenia i zarządzania protokołami serwisowymi z podpisem cyfrowym
 * Integracja z istniejącym systemem Service Orders
 */

import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useNavigate, useActionData } from "@remix-run/react";
import { useState, useEffect } from "react";
import { DigitalSignatureProtocol } from "~/components/organisms/digital-signature-protocol";
import { getServiceOrderById } from "~/services/service-order.service";
import { requireUserId } from "~/session.server";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { ArrowLeft, FileText, Download } from "lucide-react";

interface ServiceProtocolData {
  serviceOrderId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  serviceAddress: string;
  equipmentDetails: {
    brand: string;
    model: string;
    serialNumber: string;
    installationDate?: string;
  };
  serviceDetails: {
    serviceType: 'installation' | 'repair' | 'maintenance' | 'inspection';
    description: string;
    workPerformed: string;
    partsUsed: Array<{
      name: string;
      quantity: number;
      partNumber?: string;
    }>;
    laborHours: number;
    recommendations: string;
  };
  technicianDetails: {
    name: string;
    certification: string;
    employeeId: string;
  };
  qualityChecks: Array<{
    item: string;
    status: 'pass' | 'fail' | 'na';
    notes?: string;
  }>;
  photos: Array<{
    id: string;
    url: string;
    description: string;
    timestamp: string;
  }>;
}

interface SignatureData {
  technicianSignature: string;
  customerSignature: string;
  signedAt: string;
  ipAddress?: string;
  deviceInfo?: string;
}

export async function loader({ params, request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const { serviceOrderId } = params;

  if (!serviceOrderId) {
    throw new Response("Service Order ID is required", { status: 400 });
  }

  try {
    // Pobierz dane zlecenia serwisowego
    const serviceOrder = await getServiceOrderById(serviceOrderId);
    
    if (!serviceOrder) {
      throw new Response("Service Order not found", { status: 404 });
    }

    // Sprawdź czy protokół już istnieje
    const existingProtocol = await getExistingProtocol(serviceOrderId);

    // Przygotuj dane początkowe dla protokołu
    const initialProtocolData: Partial<ServiceProtocolData> = {
      serviceOrderId,
      customerName: serviceOrder.customer?.name || '',
      customerEmail: serviceOrder.customer?.email || '',
      customerPhone: serviceOrder.customer?.phone || '',
      serviceAddress: serviceOrder.customer?.address || '',
      equipmentDetails: {
        brand: serviceOrder.device?.brand || '',
        model: serviceOrder.device?.model || '',
        serialNumber: serviceOrder.device?.serialNumber || '',
      },
      serviceDetails: {
        serviceType: serviceOrder.type as any,
        description: serviceOrder.description || '',
        workPerformed: '',
        partsUsed: [],
        laborHours: 0,
        recommendations: ''
      },
      technicianDetails: {
        name: serviceOrder.assignedTechnician?.name || '',
        certification: serviceOrder.assignedTechnician?.certification || '',
        employeeId: serviceOrder.assignedTechnician?.employeeId || ''
      }
    };

    return json({
      serviceOrder,
      initialProtocolData,
      existingProtocol,
      userId
    });

  } catch (error) {
    console.error('Error loading service protocol:', error);
    throw new Response("Error loading service protocol", { status: 500 });
  }
}

export async function action({ request, params }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const { serviceOrderId } = params;
  
  if (!serviceOrderId) {
    throw new Response("Service Order ID is required", { status: 400 });
  }

  const formData = await request.formData();
  const action = formData.get("action");

  try {
    switch (action) {
      case "save_draft": {
        const protocolData = JSON.parse(formData.get("protocolData") as string);
        
        // Zapisz roboczą wersję protokołu
        const savedProtocol = await saveProtocolDraft(serviceOrderId, protocolData, userId);
        
        return json({ 
          success: true, 
          message: "Protokół zapisany jako roboczy",
          protocol: savedProtocol 
        });
      }

      case "complete_protocol": {
        const protocolData = JSON.parse(formData.get("protocolData") as string);
        const signatures = JSON.parse(formData.get("signatures") as string);
        
        // Finalizuj protokół z podpisami
        const completedProtocol = await completeProtocol(serviceOrderId, protocolData, signatures, userId);
        
        // Wygeneruj PDF
        const pdfUrl = await generateProtocolPDF(completedProtocol);
        
        // Wyślij email do klienta
        await sendProtocolToCustomer(completedProtocol, pdfUrl);
        
        // Zaktualizuj status zlecenia
        await updateServiceOrderStatus(serviceOrderId, 'COMPLETED');
        
        return json({ 
          success: true, 
          message: "Protokół zakończony i wysłany do klienta",
          protocol: completedProtocol,
          pdfUrl 
        });
      }

      case "generate_pdf": {
        const protocolId = formData.get("protocolId") as string;
        const protocol = await getProtocolById(protocolId);
        
        if (!protocol) {
          throw new Response("Protocol not found", { status: 404 });
        }
        
        const pdfUrl = await generateProtocolPDF(protocol);
        
        return json({ 
          success: true, 
          pdfUrl 
        });
      }

      default:
        throw new Response("Invalid action", { status: 400 });
    }
  } catch (error) {
    console.error('Error in protocol action:', error);
    return json({ 
      success: false, 
      error: "Wystąpił błąd podczas przetwarzania protokołu" 
    }, { status: 500 });
  }
}

export default function ServiceProtocolPage() {
  const { serviceOrder, initialProtocolData, existingProtocol } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigate = useNavigate();
  
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (actionData?.success) {
      // Pokaż powiadomienie o sukcesie
      console.log(actionData.message);
      
      if (actionData.protocol && actionData.pdfUrl) {
        // Protokół został zakończony - przekieruj do widoku zlecenia
        setTimeout(() => {
          navigate(`/service-orders/${serviceOrder.id}`);
        }, 2000);
      }
    }
  }, [actionData, navigate, serviceOrder.id]);

  const handleSaveProtocol = async (protocolData: ServiceProtocolData) => {
    setIsLoading(true);
    
    try {
      const formData = new FormData();
      formData.append("action", "save_draft");
      formData.append("protocolData", JSON.stringify(protocolData));
      
      const response = await fetch(`/service-orders/${serviceOrder.id}/protocol`, {
        method: "POST",
        body: formData
      });
      
      const result = await response.json();
      
      if (result.success) {
        console.log("Protokół zapisany pomyślnie");
      } else {
        console.error("Błąd podczas zapisywania protokołu:", result.error);
      }
    } catch (error) {
      console.error("Błąd podczas zapisywania protokołu:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCompleteProtocol = async (protocolData: ServiceProtocolData, signatures: SignatureData) => {
    setIsLoading(true);
    
    try {
      const formData = new FormData();
      formData.append("action", "complete_protocol");
      formData.append("protocolData", JSON.stringify(protocolData));
      formData.append("signatures", JSON.stringify(signatures));
      
      const response = await fetch(`/service-orders/${serviceOrder.id}/protocol`, {
        method: "POST",
        body: formData
      });
      
      const result = await response.json();
      
      if (result.success) {
        console.log("Protokół zakończony pomyślnie");
        // Automatyczne przekierowanie nastąpi w useEffect
      } else {
        console.error("Błąd podczas finalizacji protokołu:", result.error);
      }
    } catch (error) {
      console.error("Błąd podczas finalizacji protokołu:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={() => navigate(`/service-orders/${serviceOrder.id}`)}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Powrót do zlecenia
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Protokół Serwisowy
                </h1>
                <p className="text-sm text-gray-600">
                  Zlecenie: {serviceOrder.title} | Klient: {serviceOrder.customer?.name}
                </p>
              </div>
            </div>
            
            {existingProtocol && (
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Pobierz PDF
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {existingProtocol ? (
          // Pokaż istniejący protokół
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Protokół został już utworzony
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                Protokół dla tego zlecenia został już utworzony i podpisany.
              </p>
              <div className="flex gap-2">
                <Button>
                  <Download className="h-4 w-4 mr-2" />
                  Pobierz PDF
                </Button>
                <Button variant="outline">
                  Wyślij ponownie do klienta
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          // Pokaż formularz tworzenia protokołu
          <DigitalSignatureProtocol
            serviceOrderId={serviceOrder.id}
            initialData={initialProtocolData}
            onSave={handleSaveProtocol}
            onComplete={handleCompleteProtocol}
          />
        )}

        {/* Loading Overlay */}
        {isLoading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <Card className="p-6">
              <div className="flex items-center space-x-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span>Przetwarzanie protokołu...</span>
              </div>
            </Card>
          </div>
        )}

        {/* Success Message */}
        {actionData?.success && (
          <div className="fixed bottom-4 right-4 bg-green-600 text-white p-4 rounded-lg shadow-lg">
            {actionData.message}
          </div>
        )}
      </div>
    </div>
  );
}

// Helper functions (będą implementowane w osobnych plikach serwisowych)

async function getExistingProtocol(serviceOrderId: string) {
  // TODO: Implementacja sprawdzania istniejącego protokołu
  return null;
}

async function saveProtocolDraft(serviceOrderId: string, protocolData: ServiceProtocolData, userId: string) {
  // TODO: Implementacja zapisywania roboczej wersji protokołu
  return { id: 'draft_' + Date.now(), ...protocolData };
}

async function completeProtocol(serviceOrderId: string, protocolData: ServiceProtocolData, signatures: SignatureData, userId: string) {
  // TODO: Implementacja finalizacji protokołu
  return { id: 'protocol_' + Date.now(), ...protocolData, signatures };
}

async function generateProtocolPDF(protocol: any) {
  // TODO: Implementacja generowania PDF
  return '/api/protocols/' + protocol.id + '/pdf';
}

async function sendProtocolToCustomer(protocol: any, pdfUrl: string) {
  // TODO: Implementacja wysyłania email do klienta
  return true;
}

async function updateServiceOrderStatus(serviceOrderId: string, status: string) {
  // TODO: Implementacja aktualizacji statusu zlecenia
  return true;
}

async function getProtocolById(protocolId: string) {
  // TODO: Implementacja pobierania protokołu po ID
  return null;
}
