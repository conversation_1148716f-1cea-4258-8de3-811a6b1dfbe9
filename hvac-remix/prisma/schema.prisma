// HVAC CRM Prisma Schema
// Comprehensive database schema for HVAC business management

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
}

// ================================
// CORE USER MANAGEMENT
// ================================

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  role      UserRole @default(USER)
  isActive  Boolean  @default(true)
  lastLogin DateTime?
  customerId String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // MFA Support
  mfaEnabled      Boolean @default(false)
  mfaSecret       String?
  mfaRecoveryCodes String?
  mfaVerified     Boolean @default(false)

  // Relationships
  password        Password?
  notes           Note[]
  customers       Customer[]
  devices         Device[]
  serviceOrders   ServiceOrder[]
  calendarEntries CalendarEntry[]
  invoices        Invoice[]
  serviceReports  ServiceReport[]
  aiConversations AIConversation[]

  @@map("User")
}

model Password {
  hash   String
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@map("Password")
}

enum UserRole {
  USER
  ADMIN
  TECHNICIAN
  MANAGER
  CUSTOMER
}

// ================================
// CUSTOMER MANAGEMENT
// ================================

model Customer {
  id       String  @id @default(cuid())
  name     String
  email    String?
  phone    String?
  address  String?
  city     String?
  postalCode String?
  country  String?
  notes    String?

  // Portal Access
  hasPortalAccess         Boolean   @default(false)
  portalAccessInviteSent  DateTime?
  portalAccessLastLogin   DateTime?

  // Payment Integration
  stripeCustomerId String?

  // HVAC-Specific Fields
  preferredTechnician String?
  contractType        String?
  priority           CustomerPriority @default(MEDIUM)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String

  // Relationships
  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  devices       Device[]
  serviceOrders ServiceOrder[]
  invoices      Invoice[]
  opportunities Opportunity[]

  @@map("Customer")
}

enum CustomerPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

// ================================
// EQUIPMENT MANAGEMENT
// ================================

model Device {
  id                   String    @id @default(cuid())
  name                 String
  model                String?
  serialNumber         String?
  manufacturer         String?
  installationDate     DateTime?
  warrantyExpiryDate   DateTime?
  notes                String?

  // HVAC-Specific Fields
  type                 String?
  capacity             String?
  efficiency           String?
  refrigerantType      String?
  lastMaintenanceDate  DateTime?
  nextMaintenanceDate  DateTime?
  maintenanceInterval  Int?      // days

  // Visibility
  customerVisible      Boolean   @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  customerId String
  userId     String

  // Relationships
  customer      Customer       @relation(fields: [customerId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  serviceOrders ServiceOrder[]

  @@map("Device")
}

// ================================
// SERVICE MANAGEMENT
// ================================

model ServiceOrder {
  id          String             @id @default(cuid())
  title       String
  description String?
  status      ServiceOrderStatus @default(PENDING)
  priority    ServiceOrderPriority @default(MEDIUM)
  type        ServiceOrderType   @default(MAINTENANCE)

  // Scheduling
  scheduledDate   DateTime?
  completedDate   DateTime?
  estimatedHours  Float?
  actualHours     Float?

  // Assignment
  assignedTechnician String?
  technicianNotes    String?

  // Financial
  estimatedCost Float?
  actualCost    Float?
  currency      String @default("PLN")

  // Parts and Materials
  partsRequired String?
  partsUsed     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  customerId String
  userId     String
  deviceId   String?

  // Relationships
  customer       Customer        @relation(fields: [customerId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  device         Device?         @relation(fields: [deviceId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  calendarEntry  CalendarEntry?
  serviceReport  ServiceReport?

  @@map("ServiceOrder")
}

enum ServiceOrderStatus {
  PENDING
  SCHEDULED
  IN_PROGRESS
  WAITING_PARTS
  QUALITY_CHECK
  COMPLETED
  CANCELLED
  BILLED
}

enum ServiceOrderPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
  EMERGENCY
}

enum ServiceOrderType {
  MAINTENANCE
  REPAIR
  INSTALLATION
  INSPECTION
  EMERGENCY
  WARRANTY
}

// ================================
// SALES PIPELINE & OPPORTUNITIES
// ================================

model Opportunity {
  id          String            @id @default(cuid())
  name        String
  description String?

  // Sales Pipeline (7-stage)
  stage       OpportunityStage  @default(NEW_LEAD)
  probability Int               @default(0) // 0-100%
  value       Float             @default(0)
  currency    String            @default("PLN")

  // Timeline
  expectedCloseDate DateTime?
  actualCloseDate   DateTime?

  // HVAC-Specific
  serviceType         HVACServiceType @default(MAINTENANCE)
  equipmentType       String?
  installationComplexity InstallationComplexity @default(MEDIUM)
  roomCount           Int?
  totalArea           Float? // m²
  buildingType        BuildingType @default(RESIDENTIAL)

  // Assignment
  ownerId     String // Sales rep/technician
  teamId      String?

  // Lead Source
  leadSource  LeadSource @default(WEBSITE)
  referralSource String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  customerId String

  // Relationships
  customer Customer @relation(fields: [customerId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@map("Opportunity")
}

enum OpportunityStage {
  NEW_LEAD
  QUALIFIED
  PROPOSAL
  NEGOTIATION
  IN_PROGRESS
  CLOSED_WON
  FOLLOW_UP
}

enum HVACServiceType {
  MAINTENANCE
  INSTALLATION
  REPAIR
  CONSULTATION
  EMERGENCY
  UPGRADE
}

enum InstallationComplexity {
  SIMPLE
  MEDIUM
  COMPLEX
  VERY_COMPLEX
}

enum BuildingType {
  RESIDENTIAL
  COMMERCIAL
  INDUSTRIAL
  INSTITUTIONAL
}

enum LeadSource {
  WEBSITE
  PHONE
  EMAIL
  REFERRAL
  SOCIAL_MEDIA
  ADVERTISING
  TRADE_SHOW
  OTHER
}

// ================================
// CALENDAR & SCHEDULING
// ================================

model CalendarEntry {
  id          String    @id @default(cuid())
  title       String
  description String?
  startTime   DateTime
  endTime     DateTime
  location    String?
  isAllDay    Boolean   @default(false)
  color       String?

  // Calendar Categories (3 types)
  category    CalendarCategory @default(SERVICE)

  // Outlook Integration
  outlookEventId   String?
  outlookLastSync  DateTime?

  // HVAC-Specific Fields
  customer         String?
  technician       String?
  device           String?
  deviceCount      Int?
  priority         String?
  status           String?
  clientContact    String?
  technicalIssues  String?
  spareParts       String?

  // Cost Information
  costAmount      Float?
  costCurrency    String?
  costDescription String?

  // AI Analysis
  keywords           String?
  semanticAnalysis   String?
  analysisTimestamp  DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String
  serviceOrderId String? @unique

  // Relationships
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  serviceOrder ServiceOrder? @relation(fields: [serviceOrderId], references: [id], onDelete: SetNull, onUpdate: Cascade)

  @@map("CalendarEntry")
}

enum CalendarCategory {
  SERVICE      // 🔧 Serwis
  INSTALLATION // 🏗️ Nowa Instalacja
  INSPECTION   // 🔍 Oględziny
}

// ================================
// FINANCIAL MANAGEMENT
// ================================

model Invoice {
  id          String        @id @default(cuid())
  number      String        @unique
  status      InvoiceStatus @default(DRAFT)

  // Financial Details
  subtotal    Float
  taxAmount   Float
  totalAmount Float
  currency    String        @default("PLN")

  // Dates
  issueDate   DateTime
  dueDate     DateTime
  paidDate    DateTime?

  // Parties
  sellerName     String?
  sellerAddress  String?
  sellerTaxId    String?
  buyerName      String?
  buyerAddress   String?
  buyerTaxId     String?

  // Payment
  paymentMethod  String?
  paymentTerms   String?

  // Document Processing
  ocrProcessed   Boolean @default(false)
  ocrData        String? // JSON

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  customerId String
  userId     String

  // Relationships
  customer Customer @relation(fields: [customerId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  items    InvoiceItem[]

  @@map("Invoice")
}

model InvoiceItem {
  id          String @id @default(cuid())
  description String
  quantity    Float
  unitPrice   Float
  totalPrice  Float
  taxRate     Float  @default(0.23) // 23% VAT in Poland

  invoiceId String
  invoice   Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@map("InvoiceItem")
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}

// ================================
// DOCUMENT MANAGEMENT
// ================================

model ServiceReport {
  id          String @id @default(cuid())
  title       String
  description String?
  content     String?

  // Document Processing
  ocrProcessed Boolean @default(false)
  ocrData      String? // JSON

  // File Information
  fileName     String?
  fileSize     Int?
  mimeType     String?
  filePath     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String
  serviceOrderId String? @unique

  // Relationships
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  serviceOrder ServiceOrder? @relation(fields: [serviceOrderId], references: [id], onDelete: SetNull, onUpdate: Cascade)

  @@map("ServiceReport")
}

// ================================
// NOTES & COMMUNICATION
// ================================

model Note {
  id    String @id @default(cuid())
  title String
  body  String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@map("Note")
}

// ================================
// AI & SEMANTIC FEATURES
// ================================

model VectorEmbedding {
  id         String   @id @default(cuid())
  entityType String   // "customer", "device", "service_order", etc.
  entityId   String
  embedding  Float[]  // Vector embedding
  metadata   String?  // JSON metadata

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([entityType, entityId])
  @@map("VectorEmbedding")
}

model AIConversation {
  id                String @id @default(cuid())
  title             String?
  summary           String?
  contextReferences String? // JSON

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String

  // Relationships
  user     User           @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  messages AIMessage[]

  @@map("AIConversation")
}

model AIMessage {
  id      String    @id @default(cuid())
  role    AIRole
  content String

  createdAt DateTime @default(now())
  conversationId String

  // Relationships
  conversation AIConversation @relation(fields: [conversationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@map("AIMessage")
}

enum AIRole {
  USER
  ASSISTANT
  SYSTEM
}

// ================================
// SYSTEM & EVENTS
// ================================

model Event {
  id       String  @id @default(cuid())
  type     String
  data     String  // JSON
  metadata String  // JSON

  processed       Boolean   @default(false)
  processingError String?
  processedAt     DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("Event")
}
