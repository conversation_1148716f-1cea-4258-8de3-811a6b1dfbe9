import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function seed() {
  console.log('🌱 Seeding HVAC CRM database...');

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 10);
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Administrator',
      role: 'ADMIN',
      password: {
        create: {
          hash: hashedPassword,
        },
      },
    },
  });

  // Create technician users
  const technicianUsers = await Promise.all([
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: '<PERSON><PERSON>',
        role: 'TECHNICIAN',
        password: {
          create: {
            hash: await bcrypt.hash('tech123', 10),
          },
        },
      },
    }),
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Marek <PERSON>walczyk',
        role: 'TECHNICIAN',
        password: {
          create: {
            hash: await bcrypt.hash('tech123', 10),
          },
        },
      },
    }),
  ]);

  // Create sample customers
  const customers = await Promise.all([
    prisma.customer.create({
      data: {
        name: 'Biuro Rachunkowe "Księgowa"',
        email: '<EMAIL>',
        phone: '+48 22 123 45 67',
        address: 'ul. Marszałkowska 15/3',
        city: 'Warszawa',
        postalCode: '00-624',
        country: 'Polska',
        priority: 'HIGH',
        contractType: 'PREMIUM',
        userId: adminUser.id,
      },
    }),
    prisma.customer.create({
      data: {
        name: 'Restauracja "Smakosze"',
        email: '<EMAIL>',
        phone: '+48 22 987 65 43',
        address: 'ul. Nowy Świat 25',
        city: 'Warszawa',
        postalCode: '00-029',
        country: 'Polska',
        priority: 'MEDIUM',
        contractType: 'STANDARD',
        userId: adminUser.id,
      },
    }),
    prisma.customer.create({
      data: {
        name: 'Mieszkanie - Jan Kowalski',
        email: '<EMAIL>',
        phone: '+48 601 234 567',
        address: 'ul. Puławska 180/15',
        city: 'Warszawa',
        postalCode: '02-670',
        country: 'Polska',
        priority: 'LOW',
        contractType: 'BASIC',
        userId: adminUser.id,
      },
    }),
  ]);

  // Create HVAC equipment/devices
  const devices = await Promise.all([
    prisma.device.create({
      data: {
        name: 'Klimatyzator Daikin FTXM35R',
        model: 'FTXM35R',
        serialNumber: 'DK2024001',
        manufacturer: 'Daikin',
        type: 'Split AC Unit',
        capacity: '3.5 kW',
        efficiency: 'A+++',
        refrigerantType: 'R-32',
        installationDate: new Date('2024-03-15'),
        warrantyExpiryDate: new Date('2027-03-15'),
        nextMaintenanceDate: new Date('2025-03-15'),
        maintenanceInterval: 365,
        customerId: customers[0].id,
        userId: adminUser.id,
      },
    }),
    prisma.device.create({
      data: {
        name: 'Chiller LG AWHP-160',
        model: 'AWHP-160',
        serialNumber: 'LG2024002',
        manufacturer: 'LG',
        type: 'Air-Water Heat Pump',
        capacity: '16 kW',
        efficiency: 'A++',
        refrigerantType: 'R-410A',
        installationDate: new Date('2024-01-20'),
        warrantyExpiryDate: new Date('2026-01-20'),
        nextMaintenanceDate: new Date('2025-01-20'),
        maintenanceInterval: 180,
        customerId: customers[1].id,
        userId: adminUser.id,
      },
    }),
    prisma.device.create({
      data: {
        name: 'Klimatyzator Mitsubishi MSZ-LN25',
        model: 'MSZ-LN25',
        serialNumber: 'MT2024003',
        manufacturer: 'Mitsubishi',
        type: 'Wall Mount AC',
        capacity: '2.5 kW',
        efficiency: 'A+++',
        refrigerantType: 'R-32',
        installationDate: new Date('2024-06-10'),
        warrantyExpiryDate: new Date('2029-06-10'),
        nextMaintenanceDate: new Date('2025-06-10'),
        maintenanceInterval: 365,
        customerId: customers[2].id,
        userId: adminUser.id,
      },
    }),
  ]);

  // Create service orders
  const serviceOrders = await Promise.all([
    prisma.serviceOrder.create({
      data: {
        title: 'Przegląd okresowy klimatyzacji',
        description: 'Rutynowy przegląd i czyszczenie klimatyzatora Daikin',
        status: 'SCHEDULED',
        priority: 'MEDIUM',
        type: 'MAINTENANCE',
        scheduledDate: new Date('2025-01-15T10:00:00Z'),
        estimatedHours: 2,
        estimatedCost: 250.00,
        assignedTechnician: 'Tomasz Nowak',
        partsRequired: 'Filtry, środek czyszczący',
        customerId: customers[0].id,
        userId: adminUser.id,
        deviceId: devices[0].id,
      },
    }),
    prisma.serviceOrder.create({
      data: {
        title: 'Naprawa chillera - wyciek czynnika',
        description: 'Wykryto wyciek czynnika chłodniczego, wymiana uszczelki',
        status: 'IN_PROGRESS',
        priority: 'HIGH',
        type: 'REPAIR',
        scheduledDate: new Date('2025-01-10T08:00:00Z'),
        estimatedHours: 4,
        estimatedCost: 800.00,
        assignedTechnician: 'Marek Kowalczyk',
        partsRequired: 'Uszczelka, czynnik R-410A',
        customerId: customers[1].id,
        userId: adminUser.id,
        deviceId: devices[1].id,
      },
    }),
  ]);

  // Create opportunities (sales pipeline)
  const opportunities = await Promise.all([
    prisma.opportunity.create({
      data: {
        name: 'Instalacja klimatyzacji w nowym biurze',
        description: 'Klient planuje rozbudowę biura o 3 dodatkowe pomieszczenia',
        stage: 'PROPOSAL',
        probability: 75,
        value: 15000.00,
        expectedCloseDate: new Date('2025-02-28'),
        serviceType: 'INSTALLATION',
        equipmentType: 'Multi-split system',
        installationComplexity: 'MEDIUM',
        roomCount: 3,
        totalArea: 120.0,
        buildingType: 'COMMERCIAL',
        ownerId: adminUser.id,
        leadSource: 'REFERRAL',
        customerId: customers[0].id,
      },
    }),
    prisma.opportunity.create({
      data: {
        name: 'Modernizacja systemu HVAC restauracji',
        description: 'Wymiana starego systemu na energooszczędny',
        stage: 'NEGOTIATION',
        probability: 60,
        value: 25000.00,
        expectedCloseDate: new Date('2025-03-15'),
        serviceType: 'UPGRADE',
        equipmentType: 'VRF System',
        installationComplexity: 'COMPLEX',
        roomCount: 5,
        totalArea: 200.0,
        buildingType: 'COMMERCIAL',
        ownerId: adminUser.id,
        leadSource: 'WEBSITE',
        customerId: customers[1].id,
      },
    }),
  ]);

  // Create calendar entries
  await Promise.all([
    prisma.calendarEntry.create({
      data: {
        title: 'Przegląd klimatyzacji - Biuro Księgowa',
        description: 'Rutynowy przegląd i czyszczenie',
        startTime: new Date('2025-01-15T10:00:00Z'),
        endTime: new Date('2025-01-15T12:00:00Z'),
        category: 'SERVICE',
        customer: customers[0].name,
        technician: 'Tomasz Nowak',
        priority: 'MEDIUM',
        status: 'SCHEDULED',
        userId: adminUser.id,
        serviceOrderId: serviceOrders[0].id,
      },
    }),
    prisma.calendarEntry.create({
      data: {
        title: 'Naprawa chillera - Restauracja Smakosze',
        description: 'Naprawa wycieku czynnika',
        startTime: new Date('2025-01-10T08:00:00Z'),
        endTime: new Date('2025-01-10T12:00:00Z'),
        category: 'SERVICE',
        customer: customers[1].name,
        technician: 'Marek Kowalczyk',
        priority: 'HIGH',
        status: 'IN_PROGRESS',
        userId: adminUser.id,
        serviceOrderId: serviceOrders[1].id,
      },
    }),
    prisma.calendarEntry.create({
      data: {
        title: 'Oględziny nowej instalacji - Mieszkanie Kowalski',
        description: 'Wstępne oględziny przed instalacją klimatyzacji',
        startTime: new Date('2025-01-20T14:00:00Z'),
        endTime: new Date('2025-01-20T15:30:00Z'),
        category: 'INSPECTION',
        customer: customers[2].name,
        technician: 'Tomasz Nowak',
        priority: 'LOW',
        status: 'SCHEDULED',
        userId: adminUser.id,
      },
    }),
  ]);

  console.log('✅ Database seeded successfully!');
  console.log(`👤 Admin user: <EMAIL> / admin123`);
  console.log(`🔧 Technicians: <EMAIL>, <EMAIL> / tech123`);
  console.log(`🏢 Created ${customers.length} customers`);
  console.log(`🔧 Created ${devices.length} HVAC devices`);
  console.log(`📋 Created ${serviceOrders.length} service orders`);
  console.log(`💼 Created ${opportunities.length} sales opportunities`);
}

seed()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
