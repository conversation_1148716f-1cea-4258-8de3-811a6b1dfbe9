#!/usr/bin/env python3
"""
🚀 EMAIL SCAN RUNNER
Uruchamia kompletny proces skanowania 8-letniej historii emaili i generowania bazy danych
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from email_history_scanner import EmailHistoryScanner
from database_generator import DatabaseGenerator

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EmailScanRunner:
    """Główny runner dla procesu skanowania emaili"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.results = {}
    
    async def run_complete_scan(self):
        """Uruchamia kompletny proces skanowania i generowania bazy"""
        
        print("🚀" + "="*80)
        print("📧 HVAC CRM - 8-YEAR EMAIL HISTORY SCAN & DATABASE GENERATION")
        print("="*82)
        print(f"⏰ Start time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📧 Email accounts: <EMAIL>, <EMAIL>")
        print(f"🔐 Password: Blaeritipol1")
        print(f"📅 Scanning period: Last 8 years")
        print(f"🎯 Goal: Generate complete customer database from email history")
        print("="*82)
        print()
        
        try:
            # PHASE 1: Email History Scanning
            print("📧 PHASE 1: EMAIL HISTORY SCANNING")
            print("-" * 50)
            
            scanner = EmailHistoryScanner()
            scan_results = await scanner.scan_all_accounts(years_back=8)
            
            self.results['scan_phase'] = scan_results
            
            print(f"✅ Email scanning completed!")
            print(f"   📧 Total emails: {scan_results['total_emails']}")
            print(f"   ✅ Processed: {scan_results['processed_emails']}")
            print(f"   👥 Customers found: {scan_results['customers_generated']}")
            print(f"   ⏱️  Duration: {scan_results['duration_minutes']:.1f} minutes")
            print()
            
            # PHASE 2: Database Generation
            print("🗄️ PHASE 2: DATABASE GENERATION")
            print("-" * 50)
            
            generator = DatabaseGenerator()
            db_results = await generator.generate_database()
            
            self.results['database_phase'] = db_results
            
            print(f"✅ Database generation completed!")
            print(f"   👥 Customers processed: {db_results['processing_stats']['customers_processed']}")
            print(f"   🆕 New customers: {db_results['processing_stats']['customers_created']}")
            print(f"   🔄 Updated customers: {db_results['processing_stats']['customers_updated']}")
            print(f"   📍 Geocoded addresses: {db_results['processing_stats']['addresses_geocoded']}")
            print()
            
            # PHASE 3: Final Summary
            self.generate_final_summary()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Email scan failed: {e}")
            print(f"\n❌ SCAN FAILED: {e}")
            return False
    
    def generate_final_summary(self):
        """Generuje końcowe podsumowanie"""
        end_time = datetime.now()
        total_duration = end_time - self.start_time
        
        print("🎆" + "="*80)
        print("📊 FINAL SUMMARY - 8-YEAR EMAIL HISTORY TRANSFORMATION")
        print("="*82)
        print(f"⏰ Total duration: {total_duration.total_seconds() / 60:.1f} minutes")
        print()
        
        # Email scanning stats
        if 'scan_phase' in self.results:
            scan = self.results['scan_phase']
            print("📧 EMAIL SCANNING RESULTS:")
            print(f"   📧 Total emails processed: {scan['total_emails']}")
            print(f"   👥 Customer profiles generated: {scan['customers_generated']}")
            print(f"   📁 Accounts processed: {len(scan['accounts_processed'])}")
            print(f"   ❌ Errors: {scan['errors']}")
            print()
        
        # Database generation stats
        if 'database_phase' in self.results:
            db = self.results['database_phase']
            if db['success']:
                print("🗄️ DATABASE GENERATION RESULTS:")
                print(f"   👥 Total customers in database: {db['database_stats']['total_customers']}")
                print(f"   🟢 Active customers: {db['database_stats']['active_customers']}")
                print(f"   💰 Total estimated value: {db['database_stats']['total_estimated_value']:,.2f} zł")
                print(f"   🔧 Service records created: {db['database_stats']['total_service_records']}")
                print()
        
        print("🎯 BUSINESS IMPACT:")
        print("   ✅ 8 years of email history recovered and analyzed")
        print("   ✅ Complete customer database generated automatically")
        print("   ✅ Customer locations geocoded for map visualization")
        print("   ✅ Service history extracted from email communications")
        print("   ✅ Ready for HVAC-Remix map visualization")
        print()
        
        print("🚀 NEXT STEPS:")
        print("   1. Open HVAC-Remix at http://localhost:3010")
        print("   2. Navigate to Customers → View Map")
        print("   3. See all customers visualized on Warsaw map")
        print("   4. Use customer data for business intelligence")
        print()
        
        print("📁 FILES GENERATED:")
        print(f"   📧 email_history_8years.db - SQLite database with all emails")
        print(f"   🗄️ PostgreSQL hvac_customers table - Customer database")
        print(f"   📊 Scan reports in JSON format")
        print()
        
        print("🎆 TRANSFORMATION COMPLETE! Your 8-year email history is now a powerful CRM database!")
        print("="*82)

async def main():
    """Główna funkcja"""
    
    # Check dependencies
    try:
        import imaplib
        import email
        import sqlite3
        import psycopg2
        import requests
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please install required packages:")
        print("pip install psycopg2-binary requests")
        return False
    
    # Check if email database already exists
    if os.path.exists("email_history_8years.db"):
        response = input("📧 Email database already exists. Do you want to rescan? (y/N): ")
        if response.lower() != 'y':
            print("Skipping email scan, proceeding to database generation...")
            
            # Run only database generation
            generator = DatabaseGenerator()
            try:
                results = await generator.generate_database()
                print("✅ Database generation completed!")
                return True
            except Exception as e:
                print(f"❌ Database generation failed: {e}")
                return False
    
    # Run complete scan
    runner = EmailScanRunner()
    success = await runner.run_complete_scan()
    
    if success:
        print("\n🎉 SUCCESS! Your HVAC CRM now has 8 years of customer history!")
        print("🗺️ Open HVAC-Remix and check the customer map!")
    else:
        print("\n❌ Scan failed. Check the logs for details.")
    
    return success

if __name__ == "__main__":
    # Run the email scan
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
