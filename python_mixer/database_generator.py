#!/usr/bin/env python3
"""
🗄️ DATABASE GENERATOR
Automatyczne generowanie bazy danych PostgreSQL z przeskanowanych emaili
Integracja z GoSpine API i HVAC-Remix
"""

import asyncio
import sqlite3
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import requests
import psycopg2
from psycopg2.extras import RealDictCursor
import hashlib
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseGenerator:
    """Generator bazy danych z historii emaili"""
    
    def __init__(self):
        # SQLite database with email history
        self.sqlite_db = "email_history_8years.db"
        
        # PostgreSQL connection (GoSpine database)
        self.pg_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'gospine_hvac',
            'user': 'postgres',
            'password': 'postgres'
        }
        
        # GoSpine API
        self.gospine_api = "http://localhost:8081/api"
        
        # Statistics
        self.stats = {
            'customers_processed': 0,
            'customers_created': 0,
            'customers_updated': 0,
            'addresses_geocoded': 0,
            'errors': 0
        }
    
    async def generate_database(self) -> Dict[str, Any]:
        """Główna funkcja generowania bazy danych"""
        logger.info("🗄️ Starting database generation from email history...")
        
        try:
            # 1. Load customers from SQLite
            customers = self.load_customers_from_sqlite()
            logger.info(f"📧 Loaded {len(customers)} customers from email history")
            
            # 2. Setup PostgreSQL database
            self.setup_postgresql_tables()
            
            # 3. Process customers
            for customer in customers:
                try:
                    await self.process_customer(customer)
                    self.stats['customers_processed'] += 1
                    
                    if self.stats['customers_processed'] % 10 == 0:
                        logger.info(f"📊 Processed {self.stats['customers_processed']}/{len(customers)} customers")
                        
                except Exception as e:
                    logger.error(f"❌ Error processing customer {customer.get('email', 'unknown')}: {e}")
                    self.stats['errors'] += 1
            
            # 4. Generate summary report
            report = self.generate_report()
            
            logger.info("✅ Database generation completed!")
            return report
            
        except Exception as e:
            logger.error(f"❌ Database generation failed: {e}")
            raise
    
    def load_customers_from_sqlite(self) -> List[Dict[str, Any]]:
        """Ładuje klientów z bazy SQLite"""
        try:
            conn = sqlite3.connect(self.sqlite_db)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM customers 
                ORDER BY total_emails DESC, estimated_value DESC
            ''')
            
            customers = [dict(row) for row in cursor.fetchall()]
            conn.close()
            
            return customers
            
        except Exception as e:
            logger.error(f"Error loading customers from SQLite: {e}")
            return []
    
    def setup_postgresql_tables(self):
        """Tworzy tabele w PostgreSQL"""
        try:
            conn = psycopg2.connect(**self.pg_config)
            cursor = conn.cursor()
            
            # Customers table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS hvac_customers (
                    id SERIAL PRIMARY KEY,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    name VARCHAR(255),
                    phone VARCHAR(50),
                    address TEXT,
                    city VARCHAR(100),
                    postal_code VARCHAR(20),
                    latitude DECIMAL(10, 8),
                    longitude DECIMAL(11, 8),
                    company VARCHAR(255),
                    status VARCHAR(50) DEFAULT 'active',
                    service_type VARCHAR(50),
                    estimated_value DECIMAL(10, 2),
                    total_emails INTEGER DEFAULT 0,
                    first_contact TIMESTAMP,
                    last_contact TIMESTAMP,
                    notes TEXT,
                    source VARCHAR(50) DEFAULT 'email_history',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Service history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS hvac_service_history (
                    id SERIAL PRIMARY KEY,
                    customer_id INTEGER REFERENCES hvac_customers(id),
                    service_date TIMESTAMP,
                    service_type VARCHAR(50),
                    description TEXT,
                    equipment VARCHAR(255),
                    value DECIMAL(10, 2),
                    status VARCHAR(50) DEFAULT 'completed',
                    notes TEXT,
                    source VARCHAR(50) DEFAULT 'email_analysis',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Equipment registry
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS hvac_equipment (
                    id SERIAL PRIMARY KEY,
                    customer_id INTEGER REFERENCES hvac_customers(id),
                    equipment_type VARCHAR(100),
                    brand VARCHAR(100),
                    model VARCHAR(100),
                    serial_number VARCHAR(100),
                    installation_date DATE,
                    warranty_end DATE,
                    location TEXT,
                    status VARCHAR(50) DEFAULT 'active',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_hvac_customers_email ON hvac_customers(email)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_hvac_customers_city ON hvac_customers(city)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_hvac_customers_status ON hvac_customers(status)')
            
            conn.commit()
            conn.close()
            
            logger.info("✅ PostgreSQL tables created/verified")
            
        except Exception as e:
            logger.error(f"Error setting up PostgreSQL tables: {e}")
            raise
    
    async def process_customer(self, customer: Dict[str, Any]):
        """Przetwarza pojedynczego klienta"""
        try:
            # 1. Geocode address if needed
            latitude, longitude = await self.geocode_address(customer)
            
            # 2. Determine customer status
            status = self.determine_customer_status(customer)
            
            # 3. Extract postal code
            postal_code = self.extract_postal_code(customer.get('address', ''))
            
            # 4. Parse notes
            notes_list = json.loads(customer.get('notes', '[]')) if customer.get('notes') else []
            notes_text = '; '.join(notes_list) if notes_list else ''
            
            # 5. Prepare customer data
            customer_data = {
                'email': customer['email'],
                'name': customer['name'],
                'phone': customer['phone'],
                'address': customer['address'],
                'city': customer['city'],
                'postal_code': postal_code,
                'latitude': latitude,
                'longitude': longitude,
                'company': customer['company'],
                'status': status,
                'service_type': customer['service_type'],
                'estimated_value': customer['estimated_value'],
                'total_emails': customer['total_emails'],
                'first_contact': customer['first_contact'],
                'last_contact': customer['last_contact'],
                'notes': notes_text
            }
            
            # 6. Insert/update in PostgreSQL
            customer_id = self.upsert_customer(customer_data)
            
            # 7. Extract and create service history
            await self.extract_service_history(customer, customer_id)
            
            # 8. Extract equipment information
            await self.extract_equipment_info(customer, customer_id)
            
            if customer_id:
                if self.customer_exists(customer['email']):
                    self.stats['customers_updated'] += 1
                else:
                    self.stats['customers_created'] += 1
            
        except Exception as e:
            logger.error(f"Error processing customer {customer.get('email')}: {e}")
            raise
    
    async def geocode_address(self, customer: Dict[str, Any]) -> tuple[Optional[float], Optional[float]]:
        """Geokoduje adres klienta"""
        try:
            address = customer.get('address', '')
            city = customer.get('city', 'Warszawa')
            
            if not address:
                return None, None
            
            full_address = f"{address}, {city}, Polska"
            
            # Use Nominatim (OpenStreetMap) for geocoding
            url = "https://nominatim.openstreetmap.org/search"
            params = {
                'q': full_address,
                'format': 'json',
                'limit': 1
            }
            
            response = requests.get(url, params=params, timeout=10)
            data = response.json()
            
            if data and len(data) > 0:
                lat = float(data[0]['lat'])
                lon = float(data[0]['lon'])
                self.stats['addresses_geocoded'] += 1
                
                # Rate limiting
                await asyncio.sleep(1)
                
                return lat, lon
            
            return None, None
            
        except Exception as e:
            logger.error(f"Geocoding error for {customer.get('email')}: {e}")
            return None, None
    
    def determine_customer_status(self, customer: Dict[str, Any]) -> str:
        """Określa status klienta na podstawie historii"""
        try:
            last_contact = datetime.fromisoformat(customer['last_contact'])
            days_since_contact = (datetime.now() - last_contact).days
            
            total_emails = customer.get('total_emails', 0)
            estimated_value = customer.get('estimated_value', 0)
            
            # Active: recent contact and high engagement
            if days_since_contact <= 90 and total_emails >= 3:
                return 'active'
            
            # Prospect: some contact but low engagement
            elif total_emails <= 2 or estimated_value == 0:
                return 'prospect'
            
            # Inactive: no recent contact
            elif days_since_contact > 365:
                return 'inactive'
            
            # Default to active for regular customers
            else:
                return 'active'
                
        except Exception as e:
            logger.error(f"Error determining status: {e}")
            return 'active'
    
    def extract_postal_code(self, address: str) -> Optional[str]:
        """Wyciąga kod pocztowy z adresu"""
        try:
            # Polish postal code pattern: XX-XXX
            pattern = r'\b\d{2}-\d{3}\b'
            match = re.search(pattern, address)
            return match.group() if match else None
        except:
            return None
    
    def customer_exists(self, email: str) -> bool:
        """Sprawdza czy klient już istnieje"""
        try:
            conn = psycopg2.connect(**self.pg_config)
            cursor = conn.cursor()
            
            cursor.execute('SELECT id FROM hvac_customers WHERE email = %s', (email,))
            exists = cursor.fetchone() is not None
            
            conn.close()
            return exists
            
        except Exception as e:
            logger.error(f"Error checking customer existence: {e}")
            return False
    
    def upsert_customer(self, customer_data: Dict[str, Any]) -> Optional[int]:
        """Wstawia lub aktualizuje klienta"""
        try:
            conn = psycopg2.connect(**self.pg_config)
            cursor = conn.cursor()
            
            # Try to update first
            cursor.execute('''
                UPDATE hvac_customers SET
                    name = %s, phone = %s, address = %s, city = %s, postal_code = %s,
                    latitude = %s, longitude = %s, company = %s, status = %s,
                    service_type = %s, estimated_value = %s, total_emails = %s,
                    first_contact = %s, last_contact = %s, notes = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE email = %s
                RETURNING id
            ''', (
                customer_data['name'], customer_data['phone'], customer_data['address'],
                customer_data['city'], customer_data['postal_code'], customer_data['latitude'],
                customer_data['longitude'], customer_data['company'], customer_data['status'],
                customer_data['service_type'], customer_data['estimated_value'],
                customer_data['total_emails'], customer_data['first_contact'],
                customer_data['last_contact'], customer_data['notes'], customer_data['email']
            ))
            
            result = cursor.fetchone()
            
            if result:
                customer_id = result[0]
            else:
                # Insert new customer
                cursor.execute('''
                    INSERT INTO hvac_customers 
                    (email, name, phone, address, city, postal_code, latitude, longitude,
                     company, status, service_type, estimated_value, total_emails,
                     first_contact, last_contact, notes)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                ''', (
                    customer_data['email'], customer_data['name'], customer_data['phone'],
                    customer_data['address'], customer_data['city'], customer_data['postal_code'],
                    customer_data['latitude'], customer_data['longitude'], customer_data['company'],
                    customer_data['status'], customer_data['service_type'], customer_data['estimated_value'],
                    customer_data['total_emails'], customer_data['first_contact'],
                    customer_data['last_contact'], customer_data['notes']
                ))
                
                customer_id = cursor.fetchone()[0]
            
            conn.commit()
            conn.close()
            
            return customer_id
            
        except Exception as e:
            logger.error(f"Error upserting customer: {e}")
            return None
    
    async def extract_service_history(self, customer: Dict[str, Any], customer_id: int):
        """Wyciąga historię serwisów z emaili"""
        try:
            # Get emails for this customer
            conn = sqlite3.connect(self.sqlite_db)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT date, subject, body_text FROM emails 
                WHERE sender = ? 
                ORDER BY date DESC
            ''', (customer['email'],))
            
            emails = cursor.fetchall()
            conn.close()
            
            # Analyze emails for service events
            service_events = []
            
            for email_date, subject, body_text in emails:
                service_info = self.analyze_email_for_service(email_date, subject, body_text)
                if service_info:
                    service_events.append(service_info)
            
            # Insert service history
            if service_events:
                self.insert_service_history(customer_id, service_events)
                
        except Exception as e:
            logger.error(f"Error extracting service history: {e}")
    
    def analyze_email_for_service(self, email_date: str, subject: str, body_text: str) -> Optional[Dict[str, Any]]:
        """Analizuje email pod kątem informacji serwisowych"""
        try:
            text = f"{subject} {body_text or ''}".lower()
            
            # Service type detection
            service_type = 'maintenance'  # default
            
            if any(word in text for word in ['instalacja', 'montaż', 'nowy']):
                service_type = 'installation'
            elif any(word in text for word in ['naprawa', 'awaria', 'nie działa']):
                service_type = 'repair'
            elif any(word in text for word in ['przegląd', 'kontrola', 'inspekcja']):
                service_type = 'inspection'
            
            # Equipment detection
            equipment = ''
            equipment_patterns = [
                r'(klimatyzator[a-z]*\s+[\w\s]+)',
                r'(pompa\s+ciepła\s+[\w\s]+)',
                r'(wentylacja\s+[\w\s]+)',
            ]
            
            for pattern in equipment_patterns:
                match = re.search(pattern, text)
                if match:
                    equipment = match.group(1).strip()
                    break
            
            # Value detection
            value = 0
            price_patterns = [
                r'(\d+(?:\s*\d{3})*)\s*zł',
                r'kwota[:\s]+(\d+(?:\s*\d{3})*)',
            ]
            
            for pattern in price_patterns:
                match = re.search(pattern, text)
                if match:
                    try:
                        value = float(match.group(1).replace(' ', ''))
                        break
                    except:
                        pass
            
            # Only create service record if we have meaningful information
            if any([
                service_type != 'maintenance',
                equipment,
                value > 0,
                any(word in text for word in ['serwis', 'naprawa', 'instalacja', 'przegląd'])
            ]):
                return {
                    'service_date': email_date,
                    'service_type': service_type,
                    'description': subject[:500],  # Limit description length
                    'equipment': equipment,
                    'value': value
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error analyzing email for service: {e}")
            return None
    
    def insert_service_history(self, customer_id: int, service_events: List[Dict[str, Any]]):
        """Wstawia historię serwisów"""
        try:
            conn = psycopg2.connect(**self.pg_config)
            cursor = conn.cursor()
            
            for event in service_events:
                cursor.execute('''
                    INSERT INTO hvac_service_history 
                    (customer_id, service_date, service_type, description, equipment, value)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON CONFLICT DO NOTHING
                ''', (
                    customer_id,
                    event['service_date'],
                    event['service_type'],
                    event['description'],
                    event['equipment'],
                    event['value']
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error inserting service history: {e}")
    
    async def extract_equipment_info(self, customer: Dict[str, Any], customer_id: int):
        """Wyciąga informacje o sprzęcie"""
        # This would analyze emails for equipment mentions
        # For now, we'll skip this to keep the implementation focused
        pass
    
    def generate_report(self) -> Dict[str, Any]:
        """Generuje raport z procesu"""
        try:
            # Get final statistics from PostgreSQL
            conn = psycopg2.connect(**self.pg_config)
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM hvac_customers')
            total_customers = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM hvac_customers WHERE status = 'active'")
            active_customers = cursor.fetchone()[0]
            
            cursor.execute('SELECT SUM(estimated_value) FROM hvac_customers')
            total_value = cursor.fetchone()[0] or 0
            
            cursor.execute('SELECT COUNT(*) FROM hvac_service_history')
            total_services = cursor.fetchone()[0]
            
            conn.close()
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'processing_stats': self.stats,
                'database_stats': {
                    'total_customers': total_customers,
                    'active_customers': active_customers,
                    'total_estimated_value': float(total_value),
                    'total_service_records': total_services
                },
                'success': True
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating report: {e}")
            return {'success': False, 'error': str(e)}

# Main execution
async def main():
    """Główna funkcja"""
    generator = DatabaseGenerator()
    
    print("🗄️ Starting database generation from 8-year email history...")
    print("📧 Source: email_history_8years.db")
    print("🐘 Target: PostgreSQL (GoSpine HVAC database)")
    print()
    
    try:
        report = await generator.generate_database()
        
        print("\n" + "="*60)
        print("📊 DATABASE GENERATION REPORT")
        print("="*60)
        print(f"✅ Customers processed: {report['processing_stats']['customers_processed']}")
        print(f"🆕 Customers created: {report['processing_stats']['customers_created']}")
        print(f"🔄 Customers updated: {report['processing_stats']['customers_updated']}")
        print(f"📍 Addresses geocoded: {report['processing_stats']['addresses_geocoded']}")
        print(f"❌ Errors: {report['processing_stats']['errors']}")
        print()
        print(f"👥 Total customers in database: {report['database_stats']['total_customers']}")
        print(f"🟢 Active customers: {report['database_stats']['active_customers']}")
        print(f"💰 Total estimated value: {report['database_stats']['total_estimated_value']:,.2f} zł")
        print(f"🔧 Service records: {report['database_stats']['total_service_records']}")
        
        # Save report
        report_file = f"database_generation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, default=str, ensure_ascii=False)
        
        print(f"💾 Report saved to: {report_file}")
        
    except Exception as e:
        logger.error(f"❌ Database generation failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
