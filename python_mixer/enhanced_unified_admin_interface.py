#!/usr/bin/env python3
"""
🎯 ENHANCED UNIFIED ADMIN INTERFACE - HVAC CRM SYSTEM
Ulepszona wersja centralnego interfejsu administracyjnego z aktualnymi statusami

Funkcjonalności:
- Real-time monitoring wszystkich aktywnych serwisów
- Zarządzanie interfejsami (hvac-remix, hvac-solidjs-cosmic, flet)
- Status GoSpine backend i python_mixer
- Kontrola procesów i restartowanie serwisów
- Cosmic-level design z Material 3 principles

Author: HVAC CRM Team
Date: 2025-06-01
Version: 2.0 Enhanced
"""

import gradio as gr
import asyncio
import aiohttp
import redis
import psutil
import subprocess
import json
import logging
import requests
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import pandas as pd
from pathlib import Path
import time

# Konfiguracja logowania
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedUnifiedAdminInterface:
    """Ulepszona klasa interfejsu administracyjnego z aktualnymi statusami"""
    
    def __init__(self):
        self.redis_client = None
        self.services_status = {}
        self.active_interfaces = {}
        
        # Aktualna konfiguracja serwisów (na podstawie testów)
        self.services_config = {
            "GoSpine Simple Server": {"url": "http://localhost:8081/health", "port": 8081, "status": "🟢 Active"},
            "Unified Admin Interface": {"url": "http://localhost:7861", "port": 7861, "status": "🟢 Active"},
            "HVAC SolidJS Cosmic": {"url": "http://localhost:3006", "port": 3006, "status": "🟢 Active"},
            "Redis": {"url": "**************:3037", "port": 3037, "status": "🔴 Connection Refused"},
            "MongoDB": {"url": "**************:27017", "port": 27017, "status": "❓ Unknown"},
            "LM Studio": {"url": "http://*************:1234/v1/models", "port": 1234, "status": "❓ Unknown"},
            "MinIO": {"url": "http://**************:9000", "port": 9000, "status": "❓ Unknown"}
        }
        
        # Konfiguracja interfejsów
        self.interfaces_config = {
            "HVAC-Remix": {"path": "/home/<USER>/HVAC/unifikacja/hvac-remix", "port": "3000-3002", "status": "🔴 Port Conflicts"},
            "HVAC-SolidJS-Cosmic": {"path": "/home/<USER>/HVAC/unifikacja/hvac-solidjs-cosmic", "port": 3006, "status": "🟢 Running"},
            "Flet HVAC Interface": {"path": "/home/<USER>/HVAC/unifikacja/flet_hvac_interface", "port": "8550", "status": "🔴 Missing Dependencies"},
            "Cosmic Data Processing": {"path": "/home/<USER>/HVAC/unifikacja/python_mixer", "port": "7860", "status": "🔴 Gradio Version Issues"}
        }
        
        # Konfiguracja agentów AI
        self.agents_config = {
            "Bielik V3": {"url": "http://localhost:8877/v1/models", "port": 8877, "status": "❓ Unknown"},
            "Gemma3-4b": {"url": "http://localhost:8878/v1/models", "port": 8878, "status": "❓ Unknown"},
            "Gemma-3-4b-it": {"url": "http://localhost:8879/v1/models", "port": 8879, "status": "❓ Unknown"}
        }
        
        self.init_connections()
    
    def init_connections(self):
        """Inicjalizacja połączeń z serwisami"""
        try:
            self.redis_client = redis.Redis(host='**************', port=3037, decode_responses=True, socket_timeout=5)
            self.redis_client.ping()
            self.services_config["Redis"]["status"] = "🟢 Connected"
            logger.info("✅ Połączono z Redis")
        except Exception as e:
            self.services_config["Redis"]["status"] = f"🔴 {str(e)}"
            logger.error(f"❌ Błąd połączenia z Redis: {e}")
    
    async def check_service_status(self, service_name: str, config: Dict) -> Dict:
        """Sprawdza status pojedynczego serwisu"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=3)) as session:
                async with session.get(config["url"]) as response:
                    if response.status in [200, 204]:
                        return {
                            "name": service_name,
                            "status": "🟢 Online",
                            "port": config["port"],
                            "response_time": "< 1s",
                            "last_check": datetime.now().strftime("%H:%M:%S")
                        }
        except Exception as e:
            return {
                "name": service_name,
                "status": "🔴 Offline",
                "port": config.get("port", "N/A"),
                "response_time": "Timeout",
                "last_check": datetime.now().strftime("%H:%M:%S"),
                "error": str(e)[:50]
            }
    
    def get_system_metrics(self) -> Dict:
        """Pobiera metryki systemu"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                "CPU Usage": f"{cpu_percent}%",
                "Memory Usage": f"{memory.percent}%",
                "Memory Available": f"{memory.available / (1024**3):.1f} GB",
                "Disk Usage": f"{disk.percent}%",
                "Disk Free": f"{disk.free / (1024**3):.1f} GB",
                "Load Average": f"{psutil.getloadavg()[0]:.2f}" if hasattr(psutil, 'getloadavg') else "N/A",
                "Active Processes": len(psutil.pids())
            }
        except Exception as e:
            logger.error(f"Błąd pobierania metryk: {e}")
            return {"Error": str(e)}
    
    def get_interfaces_status(self) -> pd.DataFrame:
        """Pobiera status wszystkich interfejsów"""
        interfaces_data = []
        for name, config in self.interfaces_config.items():
            interfaces_data.append({
                "Interface": name,
                "Status": config["status"],
                "Port": config["port"],
                "Path": config["path"]
            })
        return pd.DataFrame(interfaces_data)
    
    def get_running_processes(self) -> List[Dict]:
        """Pobiera listę uruchomionych procesów związanych z projektem"""
        relevant_processes = []
        keywords = ['node', 'python', 'go', 'gradio', 'remix', 'vite', 'uvicorn']
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent']):
            try:
                if any(keyword in ' '.join(proc.info['cmdline']).lower() for keyword in keywords):
                    relevant_processes.append({
                        'PID': proc.info['pid'],
                        'Name': proc.info['name'],
                        'CPU%': f"{proc.info['cpu_percent']:.1f}",
                        'Memory%': f"{proc.info['memory_percent']:.1f}",
                        'Command': ' '.join(proc.info['cmdline'])[:80] + '...' if len(' '.join(proc.info['cmdline'])) > 80 else ' '.join(proc.info['cmdline'])
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return relevant_processes[:10]  # Limit to 10 most relevant
    
    def restart_interface(self, interface_name: str) -> str:
        """Restartuje wybrany interfejs"""
        try:
            if interface_name == "HVAC-SolidJS-Cosmic":
                # Kill existing process
                subprocess.run(["pkill", "-f", "vite.*hvac-solidjs-cosmic"], capture_output=True)
                time.sleep(2)
                
                # Start new process
                result = subprocess.Popen([
                    "npm", "run", "dev"
                ], cwd="/home/<USER>/HVAC/unifikacja/hvac-solidjs-cosmic")
                
                return f"✅ {interface_name} został zrestartowany (PID: {result.pid})"
            
            elif interface_name == "GoSpine Simple Server":
                # Kill existing Go process
                subprocess.run(["pkill", "-f", "simple-server"], capture_output=True)
                time.sleep(2)
                
                # Start new process
                result = subprocess.Popen([
                    "go", "run", "cmd/simple-server/main.go"
                ], cwd="/home/<USER>/HVAC/unifikacja/GoSpine")
                
                return f"✅ {interface_name} został zrestartowany (PID: {result.pid})"
            
            else:
                return f"⚠️ Restart {interface_name} nie jest jeszcze zaimplementowany"
                
        except Exception as e:
            return f"❌ Błąd restartowania {interface_name}: {str(e)}"

def create_enhanced_admin_interface():
    """Tworzy ulepszoną wersję interfejsu Gradio"""
    admin = EnhancedUnifiedAdminInterface()
    
    # Enhanced CSS with cosmic design
    cosmic_css = """
    .gradio-container {
        font-family: 'Inter', 'Segoe UI', sans-serif;
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
        color: #ffffff;
        min-height: 100vh;
    }
    
    .cosmic-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 1rem;
        margin: 1rem 0;
        text-align: center;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    }
    
    .status-card {
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        border-radius: 1rem;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    
    .metric-card {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
        border: 1px solid rgba(102, 126, 234, 0.3);
        border-radius: 0.75rem;
        padding: 1rem;
        margin: 0.5rem 0;
    }
    """
    
    def refresh_services():
        """Odświeża status serwisów"""
        try:
            services_data = []
            for name, config in admin.services_config.items():
                services_data.append({
                    "Service": name,
                    "Status": config["status"],
                    "Port": config["port"],
                    "Last Check": datetime.now().strftime("%H:%M:%S")
                })
            return pd.DataFrame(services_data)
        except Exception as e:
            logger.error(f"Błąd odświeżania serwisów: {e}")
            return pd.DataFrame({"Error": [str(e)]})
    
    def refresh_metrics():
        """Odświeża metryki systemu"""
        metrics = admin.get_system_metrics()
        return "\n".join([f"**{k}:** {v}" for k, v in metrics.items()])
    
    def refresh_processes():
        """Odświeża listę procesów"""
        processes = admin.get_running_processes()
        return pd.DataFrame(processes) if processes else pd.DataFrame({"Info": ["No relevant processes found"]})

    # Tworzenie interfejsu Gradio
    with gr.Blocks(
        title="🎯 Enhanced HVAC CRM - Unified Admin Interface",
        theme=gr.themes.Soft(),
        css=cosmic_css
    ) as interface:

        # Cosmic Header
        gr.HTML("""
        <div class="cosmic-header">
            <h1 style="font-size: 2.5rem; font-weight: 800; margin: 0;">
                🎯 Enhanced HVAC CRM - Unified Admin Interface
            </h1>
            <h2 style="font-size: 1.2rem; font-weight: 400; margin: 1rem 0 0 0; opacity: 0.9;">
                Centralny panel administracyjny z real-time monitoring
            </h2>
            <p style="font-size: 1rem; margin: 1rem 0 0 0; opacity: 0.8;">
                ⚡ Live Status • 🔄 Auto-Refresh • 🎨 Cosmic Design • 📊 Real-time Metrics
            </p>
        </div>
        """)

        with gr.Tabs():
            # Tab 1: Services & Interfaces Status
            with gr.Tab("🔧 Services & Interfaces"):
                gr.Markdown("## 🌟 Status wszystkich serwisów i interfejsów systemu")

                with gr.Row():
                    with gr.Column(scale=2):
                        services_df = gr.Dataframe(
                            value=refresh_services(),
                            headers=["Service", "Status", "Port", "Last Check"],
                            interactive=False,
                            elem_classes=["status-card"]
                        )

                    with gr.Column(scale=1):
                        interfaces_df = gr.Dataframe(
                            value=admin.get_interfaces_status(),
                            headers=["Interface", "Status", "Port", "Path"],
                            interactive=False,
                            elem_classes=["status-card"]
                        )

                with gr.Row():
                    refresh_services_btn = gr.Button("🔄 Refresh Services", variant="primary")
                    restart_service_dropdown = gr.Dropdown(
                        choices=list(admin.services_config.keys()) + list(admin.interfaces_config.keys()),
                        label="Select Service/Interface to Restart"
                    )
                    restart_btn = gr.Button("🔄 Restart Selected", variant="secondary")

                restart_output = gr.Textbox(label="Restart Output", interactive=False)

                refresh_services_btn.click(refresh_services, outputs=services_df)
                restart_btn.click(admin.restart_interface, inputs=restart_service_dropdown, outputs=restart_output)

            # Tab 2: System Metrics & Processes
            with gr.Tab("📊 System Metrics"):
                gr.Markdown("## 📈 Metryki systemu i aktywne procesy")

                with gr.Row():
                    with gr.Column(scale=1):
                        metrics_text = gr.Markdown(value=refresh_metrics(), elem_classes=["metric-card"])
                        refresh_metrics_btn = gr.Button("🔄 Refresh Metrics", variant="primary")

                    with gr.Column(scale=2):
                        processes_df = gr.Dataframe(
                            value=refresh_processes(),
                            headers=["PID", "Name", "CPU%", "Memory%", "Command"],
                            interactive=False,
                            elem_classes=["status-card"]
                        )
                        refresh_processes_btn = gr.Button("🔄 Refresh Processes", variant="primary")

                refresh_metrics_btn.click(refresh_metrics, outputs=metrics_text)
                refresh_processes_btn.click(refresh_processes, outputs=processes_df)

            # Tab 3: Quick Actions & Tools
            with gr.Tab("⚡ Quick Actions"):
                gr.Markdown("## 🚀 Szybkie akcje i narzędzia")

                with gr.Row():
                    with gr.Column():
                        gr.Markdown("### 🔧 Interface Management")
                        start_hvac_remix_btn = gr.Button("🚀 Start HVAC-Remix (Port 3010)", variant="primary")
                        start_cosmic_data_btn = gr.Button("🌟 Start Cosmic Data Processing", variant="primary")
                        start_flet_btn = gr.Button("📱 Start Flet Interface", variant="primary")

                    with gr.Column():
                        gr.Markdown("### 📊 System Tools")
                        check_ports_btn = gr.Button("🔍 Check Port Usage", variant="secondary")
                        system_cleanup_btn = gr.Button("🧹 System Cleanup", variant="secondary")
                        export_logs_btn = gr.Button("📋 Export System Logs", variant="secondary")

                action_output = gr.Textbox(label="Action Output", lines=10, interactive=False)

                # Quick action functions
                def start_hvac_remix():
                    try:
                        result = subprocess.Popen([
                            "npm", "run", "dev"
                        ], cwd="/home/<USER>/HVAC/unifikacja/hvac-remix",
                        env={**dict(os.environ), "PORT": "3010"})
                        return f"✅ HVAC-Remix started on port 3010 (PID: {result.pid})"
                    except Exception as e:
                        return f"❌ Error starting HVAC-Remix: {e}"

                def check_ports():
                    try:
                        result = subprocess.run(["netstat", "-tlnp"], capture_output=True, text=True)
                        ports_3000_range = [line for line in result.stdout.split('\n') if ':300' in line]
                        return f"Ports 3000-3009 usage:\n" + '\n'.join(ports_3000_range[:10])
                    except Exception as e:
                        return f"❌ Error checking ports: {e}"

                def start_cosmic_data():
                    try:
                        result = subprocess.Popen([
                            "python", "cosmic_data_processing_interface.py"
                        ], cwd="/home/<USER>/HVAC/unifikacja/python_mixer")
                        return f"✅ Cosmic Data Processing started (PID: {result.pid})"
                    except Exception as e:
                        return f"❌ Error starting Cosmic Data Processing: {e}"

                start_hvac_remix_btn.click(start_hvac_remix, outputs=action_output)
                check_ports_btn.click(check_ports, outputs=action_output)
                start_cosmic_data_btn.click(start_cosmic_data, outputs=action_output)

            # Tab 4: Documentation & Help
            with gr.Tab("📚 Documentation"):
                gr.Markdown("""
                ## 📖 HVAC CRM System Documentation

                ### 🎯 **Aktywne Komponenty:**
                - **Enhanced Admin Interface**: `http://localhost:7862` - Ten ulepszona wersja interfejsu
                - **Original Admin Interface**: `http://localhost:7861` - Oryginalny interfejs
                - **HVAC SolidJS Cosmic**: `http://localhost:3006` - Cosmic-level frontend
                - **GoSpine Simple Server**: `http://localhost:8081` - Backend API

                ### 🔧 **Komponenty wymagające naprawy:**
                - **HVAC-Remix**: Konflikty portów 3000-3002 → Użyj portu 3010
                - **Cosmic Data Processing**: Problemy z wersją Gradio → Wymaga aktualizacji
                - **Flet Interface**: Brakujące zależności → `pip install flet`

                ### 📋 **Następne kroki:**
                1. ✅ Uruchom Enhanced Admin Interface na porcie 7862
                2. 🔧 Napraw konflikty portów dla HVAC-Remix (użyj PORT=3010)
                3. 📦 Zainstaluj zależności dla Flet Interface
                4. 🔄 Zaktualizuj Gradio dla Cosmic Data Processing
                5. 🔗 Zintegruj wszystkie komponenty w jeden system

                ### 🚀 **Szybki start:**
                ```bash
                # Start Enhanced Admin Interface
                cd /home/<USER>/HVAC/unifikacja/python_mixer
                source venv_fixes/bin/activate
                python enhanced_unified_admin_interface.py

                # Start HVAC-Remix na wolnym porcie
                cd /home/<USER>/HVAC/unifikacja/hvac-remix
                PORT=3010 npm run dev

                # Start Flet Interface (po instalacji zależności)
                cd /home/<USER>/HVAC/unifikacja/flet_hvac_interface
                pip install flet
                python main.py
                ```

                ### 🌟 **Architektura systemu:**
                - **Frontend**: hvac-remix (React/Remix) + hvac-solidjs-cosmic (SolidJS)
                - **Backend**: GoSpine (Go/Kratos) + python_mixer (Python/FastAPI)
                - **Admin**: Enhanced Unified Admin Interface (Gradio)
                - **Mobile**: Flet HVAC Interface (Python/Flet)
                - **AI**: Bielik V3, Gemma3-4b, Cosmic Data Processing
                """)

    return interface

if __name__ == "__main__":
    interface = create_enhanced_admin_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7862,  # Nowy port żeby nie kolidować z istniejącym
        share=False,
        debug=True
    )
