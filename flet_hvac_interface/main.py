"""
🔧 HVAC CRM Flet Interface - Main Entry Point
Cutting-edge future-proof interface for backend interaction
"""

import flet as ft
import asyncio
import logging
import threading
import time
from datetime import datetime

# Import interface components with graceful error handling
import logging

logger = logging.getLogger(__name__)

# Import interfaces with fallbacks
try:
    from flet_hvac_interface.interfaces.dashboard import DashboardInterface
except ImportError as e:
    logger.warning(f"Failed to import DashboardInterface: {e}")
    DashboardInterface = None

try:
    from flet_hvac_interface.interfaces.customer_profiles import CustomerProfilesInterface
except ImportError as e:
    logger.warning(f"Failed to import CustomerProfilesInterface: {e}")
    CustomerProfilesInterface = None

try:
    from flet_hvac_interface.interfaces.email_intelligence import EmailIntelligenceInterface
except ImportError as e:
    logger.warning(f"Failed to import EmailIntelligenceInterface: {e}")
    EmailIntelligenceInterface = None

try:
    from flet_hvac_interface.interfaces.service_orders import ServiceOrdersInterface
except ImportError as e:
    logger.warning(f"Failed to import ServiceOrdersInterface: {e}")
    ServiceOrdersInterface = None

try:
    from flet_hvac_interface.interfaces.equipment_registry import EquipmentRegistryInterface
except ImportError as e:
    logger.warning(f"Failed to import EquipmentRegistryInterface: {e}")
    EquipmentRegistryInterface = None

try:
    from flet_hvac_interface.interfaces.financial_dashboard import FinancialDashboardInterface
except ImportError as e:
    logger.warning(f"Failed to import FinancialDashboardInterface: {e}")
    FinancialDashboardInterface = None

try:
    from flet_hvac_interface.interfaces.calendar_management import CalendarManagementInterface
except ImportError as e:
    logger.warning(f"Failed to import CalendarManagementInterface: {e}")
    CalendarManagementInterface = None

try:
    from flet_hvac_interface.interfaces.mobile_technician_app import MobileTechnicianApp
except ImportError as e:
    logger.warning(f"Failed to import MobileTechnicianApp: {e}")
    MobileTechnicianApp = None

# Configure logging if not already configured
if not logging.getLogger().handlers:
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class HVACCRMInterface:
    """Main HVAC CRM Flet Interface Application"""
    
    def __init__(self):
        self.page = None
        self.current_view = "dashboard"
        self.data_sources_status = {
            "email_monitor": {"status": "running", "last_update": datetime.now()},
            "transcription": {"status": "idle", "last_update": datetime.now()},
            "ai_analysis": {"status": "processing", "last_update": datetime.now()},
            "database_sync": {"status": "syncing", "last_update": datetime.now()}
        }
        self.threads = {}
        self.running = True

        # Initialize interface components (will be created when page is available)
        self.dashboard_interface = None
        self.customer_profiles_interface = None
        self.email_intelligence_interface = None
        self.service_orders_interface = None
        self.equipment_registry_interface = None
        self.financial_dashboard_interface = None
        self.calendar_management_interface = None
        self.mobile_technician_app = None

        logger.info("🚀 HVAC CRM Interface initialized")
    
    def main(self, page: ft.Page):
        """Main application entry point"""
        self.page = page

        # Configure page
        page.title = "🔧 HVAC CRM - Kosmiczny Interfejs"
        page.window_width = 1400
        page.window_height = 900
        page.theme_mode = ft.ThemeMode.DARK
        page.padding = 0

        # Set theme and create layout
        page.theme = ft.Theme(color_scheme_seed="#2196F3", use_material3=True)

        # Initialize interface components
        self._initialize_interfaces()

        self.create_main_layout()
        self.start_background_threads()

        logger.info("✅ HVAC CRM Interface started successfully")

    def _initialize_interfaces(self):
        """Initialize all interface components with graceful error handling"""
        interfaces_initialized = 0

        # Initialize dashboard interface
        if DashboardInterface:
            try:
                self.dashboard_interface = DashboardInterface(self.page)
                interfaces_initialized += 1
                logger.info("✅ Dashboard interface initialized")
            except Exception as e:
                logger.error(f"❌ Error initializing dashboard interface: {e}")
                self.dashboard_interface = None
        else:
            self.dashboard_interface = None

        # Initialize customer profiles interface
        if CustomerProfilesInterface:
            try:
                self.customer_profiles_interface = CustomerProfilesInterface(self.page)
                interfaces_initialized += 1
                logger.info("✅ Customer profiles interface initialized")
            except Exception as e:
                logger.error(f"❌ Error initializing customer profiles interface: {e}")
                self.customer_profiles_interface = None
        else:
            self.customer_profiles_interface = None

        # Initialize email intelligence interface
        if EmailIntelligenceInterface:
            try:
                self.email_intelligence_interface = EmailIntelligenceInterface(self.page)
                interfaces_initialized += 1
                logger.info("✅ Email intelligence interface initialized")
            except Exception as e:
                logger.error(f"❌ Error initializing email intelligence interface: {e}")
                self.email_intelligence_interface = None
        else:
            self.email_intelligence_interface = None

        # Initialize service orders interface
        if ServiceOrdersInterface:
            try:
                self.service_orders_interface = ServiceOrdersInterface(self.page)
                interfaces_initialized += 1
                logger.info("✅ Service orders interface initialized")
            except Exception as e:
                logger.error(f"❌ Error initializing service orders interface: {e}")
                self.service_orders_interface = None
        else:
            self.service_orders_interface = None

        # Initialize equipment registry interface
        if EquipmentRegistryInterface:
            try:
                self.equipment_registry_interface = EquipmentRegistryInterface(self.page)
                interfaces_initialized += 1
                logger.info("✅ Equipment registry interface initialized")
            except Exception as e:
                logger.error(f"❌ Error initializing equipment registry interface: {e}")
                self.equipment_registry_interface = None
        else:
            self.equipment_registry_interface = None

        # Initialize financial dashboard interface
        if FinancialDashboardInterface:
            try:
                self.financial_dashboard_interface = FinancialDashboardInterface(self.page)
                interfaces_initialized += 1
                logger.info("✅ Financial dashboard interface initialized")
            except Exception as e:
                logger.error(f"❌ Error initializing financial dashboard interface: {e}")
                self.financial_dashboard_interface = None
        else:
            self.financial_dashboard_interface = None

        # Initialize calendar management interface
        if CalendarManagementInterface:
            try:
                self.calendar_management_interface = CalendarManagementInterface(self.page)
                interfaces_initialized += 1
                logger.info("✅ Calendar management interface initialized")
            except Exception as e:
                logger.error(f"❌ Error initializing calendar management interface: {e}")
                self.calendar_management_interface = None
        else:
            self.calendar_management_interface = None

        logger.info(f"✅ Initialized {interfaces_initialized}/7 interface components")
    def create_main_layout(self):
        """Create the main application layout with cosmic-level design"""
        # Navigation rail with cosmic styling
        nav_rail = ft.NavigationRail(
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=100,
            min_extended_width=200,
            group_alignment=-0.9,
            destinations=[
                ft.NavigationRailDestination(
                    icon=ft.Icons.DASHBOARD_OUTLINED,
                    selected_icon=ft.Icons.DASHBOARD,
                    label="Panel Główny"
                ),
                ft.NavigationRailDestination(
                    icon=ft.Icons.PEOPLE_OUTLINED,
                    selected_icon=ft.Icons.PEOPLE,
                    label="Klienci"
                ),
                ft.NavigationRailDestination(
                    icon=ft.Icons.EMAIL_OUTLINED,
                    selected_icon=ft.Icons.EMAIL,
                    label="Email AI"
                ),
                ft.NavigationRailDestination(
                    icon=ft.Icons.ASSIGNMENT_OUTLINED,
                    selected_icon=ft.Icons.ASSIGNMENT,
                    label="Zlecenia"
                ),
                ft.NavigationRailDestination(
                    icon=ft.Icons.SETTINGS_OUTLINED,
                    selected_icon=ft.Icons.SETTINGS,
                    label="Sprzęt"
                ),
                ft.NavigationRailDestination(
                    icon=ft.Icons.ATTACH_MONEY_OUTLINED,
                    selected_icon=ft.Icons.ATTACH_MONEY,
                    label="Finanse"
                ),
                ft.NavigationRailDestination(
                    icon=ft.Icons.CALENDAR_MONTH_OUTLINED,
                    selected_icon=ft.Icons.CALENDAR_MONTH,
                    label="Kalendarz"
                ),
                ft.NavigationRailDestination(
                    icon=ft.Icons.PHONE_ANDROID_OUTLINED,
                    selected_icon=ft.Icons.PHONE_ANDROID,
                    label="Mobilny"
                ),
            ],
            on_change=self.navigation_changed,
        )
        
        # Main content area
        self.content_area = ft.Container(
            content=self.create_dashboard_view(),
            expand=True,
            padding=20,
        )
        
        # Main layout
        main_layout = ft.Row(
            [
                nav_rail,
                ft.VerticalDivider(width=1),
                self.content_area,
            ],
            expand=True,
        )
        
        self.page.add(main_layout)
        self.page.update()    
    def navigation_changed(self, e):
        """Handle navigation changes"""
        views = ["dashboard", "customers", "email_intel", "service_orders", "equipment", "financial", "calendar", "mobile"]
        self.current_view = views[e.control.selected_index]

        # Update content based on selection
        try:
            if self.current_view == "dashboard":
                if self.dashboard_interface:
                    self.content_area.content = self.dashboard_interface.create_interface()
                else:
                    self.content_area.content = self.create_dashboard_view()
            elif self.current_view == "customers":
                if self.customer_profiles_interface:
                    self.content_area.content = self.customer_profiles_interface.create_interface()
                else:
                    self.content_area.content = self.create_customers_view()
            elif self.current_view == "email_intel":
                if self.email_intelligence_interface:
                    self.content_area.content = self.email_intelligence_interface.create_interface()
                else:
                    self.content_area.content = self.create_email_intelligence_view()
            elif self.current_view == "service_orders":
                if self.service_orders_interface:
                    self.content_area.content = self.service_orders_interface.create_interface()
                else:
                    self.content_area.content = self.create_service_orders_view()
            elif self.current_view == "equipment":
                if self.equipment_registry_interface:
                    self.content_area.content = self.equipment_registry_interface.create_interface()
                else:
                    self.content_area.content = self.create_equipment_view()
            elif self.current_view == "financial":
                if self.financial_dashboard_interface:
                    self.content_area.content = self.financial_dashboard_interface.create_interface()
                else:
                    self.content_area.content = self.create_financial_view()
            elif self.current_view == "calendar":
                if self.calendar_management_interface:
                    self.content_area.content = self.calendar_management_interface.create_interface()
                else:
                    self.content_area.content = self.create_calendar_view()
            elif self.current_view == "mobile":
                if MobileTechnicianApp:
                    if not self.mobile_technician_app:
                        self.mobile_technician_app = MobileTechnicianApp(self.page)
                    self.content_area.content = self.mobile_technician_app.create_interface()
                else:
                    self.content_area.content = self.create_mobile_view()
        except Exception as e:
            logger.error(f"❌ Error switching to view {self.current_view}: {e}")
            self.content_area.content = self.create_error_view(str(e))

        self.page.update()
        logger.info(f"🔄 Switched to view: {self.current_view}")
    
    def create_dashboard_view(self):
        """Create the main dashboard view"""
        # Status cards for data sources
        status_cards = ft.Row([
            self.create_status_card("📧 Email Monitor", "email_monitor", ft.Colors.GREEN),
            self.create_status_card("🎤 Transcription", "transcription", ft.Colors.ORANGE),
            self.create_status_card("🤖 AI Analysis", "ai_analysis", ft.Colors.BLUE),
            self.create_status_card("💾 Database Sync", "database_sync", ft.Colors.PURPLE),
        ], spacing=20)
        
        # Real-time metrics
        metrics_section = ft.Container(
            content=ft.Column([
                ft.Text("📊 Real-time Metrics", size=24, weight=ft.FontWeight.BOLD),
                ft.Row([
                    self.create_metric_card("Emails Processed", "1,247", "+12%"),
                    self.create_metric_card("Transcriptions", "89", "+5%"),
                    self.create_metric_card("Customer Profiles", "456", "+8%"),
                    self.create_metric_card("AI Insights", "234", "+15%"),
                ], spacing=20),
            ]),
            padding=20,
            margin=ft.margin.only(top=20),
        )
        
        return ft.Column([
            ft.Text("🔧 HVAC CRM - Cosmic Dashboard", size=32, weight=ft.FontWeight.BOLD),
            status_cards,
            metrics_section,
        ], spacing=20)    
    def create_status_card(self, title, status_key, color):
        """Create a status card for data sources"""
        status = self.data_sources_status[status_key]
        status_color = ft.Colors.GREEN if status["status"] == "running" else ft.Colors.ORANGE
        
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Text(title, size=16, weight=ft.FontWeight.BOLD),
                    ft.Row([
                        ft.Icon(ft.Icons.CIRCLE, color=status_color, size=12),
                        ft.Text(status["status"].upper(), size=12),
                    ]),
                    ft.Text(f"Last: {status['last_update'].strftime('%H:%M:%S')}", size=10),
                ]),
                padding=15,
                width=200,
            ),
            elevation=3,
        )
    
    def create_metric_card(self, title, value, change):
        """Create a metric card"""
        change_color = ft.Colors.GREEN if change.startswith('+') else ft.Colors.RED
        
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Text(title, size=14),
                    ft.Text(value, size=24, weight=ft.FontWeight.BOLD),
                    ft.Text(change, size=12, color=change_color),
                ]),
                padding=15,
                width=180,
            ),
            elevation=2,
        )
    
    def create_customers_view(self):
        """Create customer profiles view"""
        return ft.Column([
            ft.Text("👥 Customer Profiles - 360° View", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("Comprehensive customer data integration", size=16),
            ft.Container(
                content=ft.Text("🚧 Customer profiles interface coming soon...", size=18),
                padding=20,
                bgcolor=ft.Colors.BLUE_GREY_100,
                border_radius=10,
            ),
        ], spacing=20)
    
    def create_email_intelligence_view(self):
        """Create email intelligence view"""
        return ft.Column([
            ft.Text("📧 Email Intelligence", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("AI-powered email analysis and processing", size=16),
            ft.Container(
                content=ft.Text("🤖 Email intelligence dashboard coming soon...", size=18),
                padding=20,
                bgcolor=ft.Colors.GREEN_100,
                border_radius=10,
            ),
        ], spacing=20)    
    def create_documents_view(self):
        """Create document processing view"""
        return ft.Column([
            ft.Text("📄 Document Processing", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("OCR, invoice extraction, and document analysis", size=16),
            ft.Container(
                content=ft.Text("📋 Document processing interface coming soon...", size=18),
                padding=20,
                bgcolor=ft.Colors.ORANGE_100,
                border_radius=10,
            ),
        ], spacing=20)
    
    def create_calendar_view(self):
        """Create calendar integration view"""
        return ft.Column([
            ft.Text("📅 Calendar Integration", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("Intelligent scheduling and calendar optimization", size=16),
            ft.Container(
                content=ft.Text("🗓️ Calendar management interface coming soon...", size=18),
                padding=20,
                bgcolor=ft.Colors.PURPLE_100,
                border_radius=10,
            ),
        ], spacing=20)
    
    def create_analytics_view(self):
        """Create analytics dashboard view"""
        return ft.Column([
            ft.Text("📊 Analytics Dashboard", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("Real-time insights and business intelligence", size=16),
            ft.Container(
                content=ft.Text("📈 Analytics dashboard coming soon...", size=18),
                padding=20,
                bgcolor=ft.Colors.CYAN_100,
                border_radius=10,
            ),
        ], spacing=20)
    
    def create_service_orders_view(self):
        """Create service orders fallback view"""
        return ft.Column([
            ft.Text("🔧 Service Orders", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("Kanban workflow management", size=16),
            ft.Container(
                content=ft.Text("🚧 Service orders interface loading...", size=18),
                padding=20,
                bgcolor=ft.Colors.BLUE_100,
                border_radius=10,
            ),
        ], spacing=20)

    def create_equipment_view(self):
        """Create equipment registry fallback view"""
        return ft.Column([
            ft.Text("🔧 Equipment Registry", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("HVAC equipment lifecycle management", size=16),
            ft.Container(
                content=ft.Text("🚧 Equipment registry interface loading...", size=18),
                padding=20,
                bgcolor=ft.Colors.TEAL_100,
                border_radius=10,
            ),
        ], spacing=20)

    def create_financial_view(self):
        """Create financial dashboard fallback view"""
        return ft.Column([
            ft.Text("💰 Panel Finansowy", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("Analityka biznesowa i zarządzanie finansami", size=16),
            ft.Container(
                content=ft.Text("🚧 Interfejs panelu finansowego ładuje się...", size=18),
                padding=20,
                bgcolor=ft.Colors.GREEN_100,
                border_radius=10,
            ),
        ], spacing=20)

    def create_mobile_view(self):
        """Create mobile technician app fallback view"""
        return ft.Column([
            ft.Text("📱 Mobilna Aplikacja Technika", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("Dedykowana aplikacja dla techników HVAC", size=16),
            ft.Container(
                content=ft.Text("🚧 Aplikacja mobilna ładuje się...", size=18),
                padding=20,
                bgcolor=ft.Colors.PURPLE_100,
                border_radius=10,
            ),
        ], spacing=20)

    def create_calendar_view(self):
        """Create calendar management fallback view"""
        return ft.Column([
            ft.Text("📅 Kalendarz HVAC", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("Zarządzanie harmonogramem: Serwis | Nowa Instalacja | Oględziny", size=16),
            ft.Container(
                content=ft.Text("🚧 Interfejs kalendarza ładuje się...", size=18),
                padding=20,
                bgcolor=ft.Colors.BLUE_100,
                border_radius=10,
            ),
        ], spacing=20)

    def create_error_view(self, error_message: str):
        """Create error view"""
        return ft.Column([
            ft.Text("❌ Interface Error", size=28, weight=ft.FontWeight.BOLD, color=ft.Colors.RED),
            ft.Text("An error occurred while loading the interface", size=16),
            ft.Container(
                content=ft.Column([
                    ft.Text("Error Details:", weight=ft.FontWeight.BOLD),
                    ft.Text(error_message, size=12, color=ft.Colors.RED),
                    ft.ElevatedButton(
                        "Retry",
                        on_click=lambda e: self.page.update()
                    )
                ], spacing=10),
                padding=20,
                bgcolor=ft.Colors.RED_100,
                border_radius=10,
            ),
        ], spacing=20)
    def start_background_threads(self):
        """Start background processing threads"""
        # Email monitoring thread
        email_thread = threading.Thread(
            target=self.email_monitoring_worker,
            name="EmailMonitor",
            daemon=True
        )
        email_thread.start()
        self.threads['email'] = email_thread
        
        # Real-time updates thread
        updates_thread = threading.Thread(
            target=self.real_time_updates_worker,
            name="RealTimeUpdates",
            daemon=True
        )
        updates_thread.start()
        self.threads['updates'] = updates_thread
        
        logger.info("🧵 Background threads started")
    
    def email_monitoring_worker(self):
        """Background worker for email monitoring"""
        while self.running:
            try:
                # Simulate email processing
                time.sleep(5)
                self.data_sources_status["email_monitor"]["last_update"] = datetime.now()
                logger.info("📧 Email monitoring tick")
            except Exception as e:
                logger.error(f"❌ Email monitoring error: {e}")
    
    def real_time_updates_worker(self):
        """Background worker for real-time UI updates"""
        while self.running:
            try:
                # Update UI every 2 seconds
                time.sleep(2)
                if self.page and self.current_view == "dashboard":
                    # Update timestamps in status cards
                    for status in self.data_sources_status.values():
                        if status["status"] == "running":
                            status["last_update"] = datetime.now()
                    
                    # Trigger UI update (in real app, use page.update() carefully)
                    logger.debug("🔄 Real-time update tick")
            except Exception as e:
                logger.error(f"❌ Real-time updates error: {e}")

# Application entry point
def main():
    """Launch the HVAC CRM Flet interface"""
    app = HVACCRMInterface()
    ft.app(target=app.main, view=ft.AppView.FLET_APP)

if __name__ == "__main__":
    main()