"""
📱 MOBILE TECHNICIAN APP
Dedykowana aplikacja mobilna dla techników HVAC z protokołami serwisowymi
Inspirowana SerwisPlanner.pl - mobilny program do serwisu
"""

import flet as ft
import json
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import base64
import io

logger = logging.getLogger(__name__)

class MobileTechnicianApp:
    """Mobilna aplikacja dla techników HVAC"""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.current_technician = {
            "id": "tech_001",
            "name": "<PERSON><PERSON>",
            "certification": "HVAC Master",
            "phone": "+48 123 456 789",
            "location": {"lat": 52.2297, "lng": 21.0122}  # Warszawa
        }
        
        # Stan aplikacji
        self.current_view = "dashboard"
        self.selected_service_order = None
        self.offline_mode = False
        self.gps_enabled = True
        
        # Dane demo
        self.service_orders = self._get_demo_service_orders()
        self.route_optimization = self._get_demo_route()
        
        # Protok<PERSON>ł serwisowy
        self.current_protocol = None
        self.signature_canvas_data = None
        
    def create_interface(self) -> ft.Column:
        """Tworzy główny interfejs mobilnej aplikacji"""
        
        # Konfiguracja mobilna
        self.page.window_width = 390  # iPhone 14 Pro width
        self.page.window_height = 844  # iPhone 14 Pro height
        self.page.padding = 0
        self.page.theme_mode = ft.ThemeMode.LIGHT
        
        return ft.Column([
            self._create_mobile_header(),
            self._create_main_content(),
            self._create_mobile_navigation()
        ], spacing=0, expand=True)
    
    def _create_mobile_header(self) -> ft.Container:
        """Tworzy nagłówek mobilny z statusem"""
        return ft.Container(
            content=ft.Row([
                ft.Column([
                    ft.Text(f"👋 {self.current_technician['name']}", 
                           size=16, weight=ft.FontWeight.BOLD),
                    ft.Row([
                        ft.Icon(ft.Icons.LOCATION_ON, size=12, color=ft.Colors.GREEN),
                        ft.Text("GPS: Aktywny", size=10),
                        ft.Icon(ft.Icons.WIFI, size=12, 
                               color=ft.Colors.GREEN if not self.offline_mode else ft.Colors.RED),
                        ft.Text("Online" if not self.offline_mode else "Offline", size=10)
                    ], spacing=5)
                ], spacing=2),
                ft.IconButton(
                    icon=ft.Icons.SYNC,
                    tooltip="Synchronizuj dane",
                    on_click=self._sync_data
                )
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(15),
            bgcolor=ft.Colors.BLUE_50,
            border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.BLUE_200))
        )
    
    def _create_main_content(self) -> ft.Container:
        """Tworzy główną zawartość w zależności od aktualnego widoku"""
        content_map = {
            "dashboard": self._create_dashboard_view,
            "orders": self._create_orders_view,
            "route": self._create_route_view,
            "protocol": self._create_protocol_view,
            "profile": self._create_profile_view
        }
        
        return ft.Container(
            content=content_map.get(self.current_view, self._create_dashboard_view)(),
            expand=True,
            padding=ft.padding.all(10)
        )
    
    def _create_mobile_navigation(self) -> ft.Container:
        """Tworzy dolną nawigację mobilną"""
        return ft.Container(
            content=ft.Row([
                self._create_nav_button("🏠", "Dashboard", "dashboard"),
                self._create_nav_button("📋", "Zlecenia", "orders"),
                self._create_nav_button("🗺️", "Trasa", "route"),
                self._create_nav_button("📝", "Protokół", "protocol"),
                self._create_nav_button("👤", "Profil", "profile"),
            ], alignment=ft.MainAxisAlignment.SPACE_AROUND),
            padding=ft.padding.all(10),
            bgcolor=ft.Colors.WHITE,
            border=ft.border.only(top=ft.BorderSide(1, ft.Colors.GREY_300))
        )
    
    def _create_nav_button(self, icon: str, label: str, view: str) -> ft.Column:
        """Tworzy przycisk nawigacji"""
        is_active = self.current_view == view
        
        return ft.Column([
            ft.Container(
                content=ft.Text(icon, size=20),
                padding=5,
                bgcolor=ft.Colors.BLUE_100 if is_active else None,
                border_radius=8
            ),
            ft.Text(label, size=10, 
                   color=ft.Colors.BLUE if is_active else ft.Colors.GREY_600)
        ], spacing=2, horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        on_click=lambda e, v=view: self._change_view(v))
    
    def _create_dashboard_view(self) -> ft.Column:
        """Tworzy widok dashboardu technika"""
        return ft.Column([
            # Dzisiejsze zlecenia
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Text("📅 Dzisiejsze zlecenia", size=18, weight=ft.FontWeight.BOLD),
                            ft.Chip(label=ft.Text("5"), bgcolor=ft.Colors.BLUE_100)
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        
                        # Lista zleceń na dziś
                        *[self._create_order_card(order) for order in self.service_orders[:3]]
                    ], spacing=10),
                    padding=15
                )
            ),
            
            # Szybkie akcje
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text("⚡ Szybkie akcje", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row([
                            self._create_quick_action("📍", "Nawigacja", self._start_navigation),
                            self._create_quick_action("📞", "Kontakt", self._call_customer),
                            self._create_quick_action("📝", "Protokół", self._start_protocol),
                            self._create_quick_action("📷", "Zdjęcie", self._take_photo),
                        ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
                    ], spacing=10),
                    padding=15
                )
            ),
            
            # Statystyki dnia
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text("📊 Statystyki dnia", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row([
                            self._create_stat_item("Wykonane", "3", ft.Colors.GREEN),
                            self._create_stat_item("W trakcie", "1", ft.Colors.ORANGE),
                            self._create_stat_item("Zaplanowane", "1", ft.Colors.BLUE),
                        ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
                    ], spacing=10),
                    padding=15
                )
            )
        ], spacing=10, scroll=ft.ScrollMode.AUTO)
    
    def _create_orders_view(self) -> ft.Column:
        """Tworzy widok listy zleceń"""
        return ft.Column([
            ft.Row([
                ft.Text("📋 Moje zlecenia", size=20, weight=ft.FontWeight.BOLD),
                ft.IconButton(
                    icon=ft.Icons.REFRESH,
                    tooltip="Odśwież",
                    on_click=self._refresh_orders
                )
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            
            # Filtry
            ft.Row([
                ft.Chip(label=ft.Text("Wszystkie"), selected=True),
                ft.Chip(label=ft.Text("Dziś")),
                ft.Chip(label=ft.Text("Jutro")),
                ft.Chip(label=ft.Text("W trakcie")),
            ], scroll=ft.ScrollMode.AUTO),
            
            # Lista zleceń
            ft.Column([
                self._create_detailed_order_card(order) 
                for order in self.service_orders
            ], spacing=10, scroll=ft.ScrollMode.AUTO, expand=True)
        ], spacing=10)
    
    def _create_route_view(self) -> ft.Column:
        """Tworzy widok optymalizacji trasy"""
        return ft.Column([
            ft.Text("🗺️ Optymalizacja trasy", size=20, weight=ft.FontWeight.BOLD),
            
            # Mapa (placeholder)
            ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.MAP, size=50, color=ft.Colors.BLUE),
                    ft.Text("Mapa z trasą", size=16),
                    ft.Text("📍 5 punktów na trasie", size=12, color=ft.Colors.GREY_600)
                ], alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                height=200,
                bgcolor=ft.Colors.BLUE_50,
                border_radius=10,
                alignment=ft.alignment.center
            ),
            
            # Informacje o trasie
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Text("📊 Informacje o trasie", weight=ft.FontWeight.BOLD),
                            ft.ElevatedButton("Rozpocznij", on_click=self._start_route)
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        
                        ft.Row([
                            self._create_route_stat("Dystans", "45 km"),
                            self._create_route_stat("Czas", "3h 20min"),
                            self._create_route_stat("Punkty", "5"),
                        ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
                    ], spacing=10),
                    padding=15
                )
            ),
            
            # Lista punktów trasy
            ft.Text("📍 Punkty na trasie", size=16, weight=ft.FontWeight.BOLD),
            ft.Column([
                self._create_route_point(i+1, order) 
                for i, order in enumerate(self.service_orders[:5])
            ], spacing=5)
        ], spacing=10, scroll=ft.ScrollMode.AUTO)
    
    def _create_protocol_view(self) -> ft.Column:
        """Tworzy widok protokołu serwisowego"""
        if not self.selected_service_order:
            return ft.Column([
                ft.Container(
                    content=ft.Column([
                        ft.Icon(ft.Icons.ASSIGNMENT, size=50, color=ft.Colors.GREY),
                        ft.Text("Wybierz zlecenie", size=18),
                        ft.Text("Aby utworzyć protokół serwisowy", size=12, color=ft.Colors.GREY_600)
                    ], alignment=ft.MainAxisAlignment.CENTER,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    expand=True,
                    alignment=ft.alignment.center
                )
            ])
        
        return ft.Column([
            ft.Row([
                ft.IconButton(icon=ft.Icons.ARROW_BACK, on_click=self._back_to_orders),
                ft.Text("📝 Protokół serwisowy", size=18, weight=ft.FontWeight.BOLD),
                ft.IconButton(icon=ft.Icons.SAVE, on_click=self._save_protocol)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            
            # Informacje o zleceniu
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(f"🏠 {self.selected_service_order['customer']}", weight=ft.FontWeight.BOLD),
                        ft.Text(f"📍 {self.selected_service_order['address']}"),
                        ft.Text(f"🔧 {self.selected_service_order['equipment']}")
                    ], spacing=5),
                    padding=10
                )
            ),
            
            # Formularz protokołu
            ft.Column([
                ft.TextField(
                    label="Wykonane prace",
                    multiline=True,
                    min_lines=3,
                    max_lines=5
                ),
                ft.TextField(
                    label="Użyte części",
                    multiline=True,
                    min_lines=2
                ),
                ft.TextField(
                    label="Zalecenia",
                    multiline=True,
                    min_lines=2
                ),
                
                # Kontrola jakości
                ft.Text("✅ Kontrola jakości", size=16, weight=ft.FontWeight.BOLD),
                *[self._create_quality_check(check) for check in [
                    "Sprawdzenie działania systemu",
                    "Test bezpieczeństwa", 
                    "Kontrola szczelności",
                    "Pomiar parametrów"
                ]],
                
                # Zdjęcia
                ft.Row([
                    ft.Text("📷 Dokumentacja foto", size=16, weight=ft.FontWeight.BOLD),
                    ft.IconButton(icon=ft.Icons.ADD_A_PHOTO, on_click=self._add_photo)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                
                # Podpis klienta
                ft.Text("✍️ Podpis klienta", size=16, weight=ft.FontWeight.BOLD),
                ft.Container(
                    content=ft.Text("Dotknij aby podpisać", 
                                   color=ft.Colors.GREY_600, text_align=ft.TextAlign.CENTER),
                    height=100,
                    bgcolor=ft.Colors.GREY_100,
                    border=ft.border.all(1, ft.Colors.GREY_300),
                    border_radius=5,
                    alignment=ft.alignment.center,
                    on_click=self._open_signature_pad
                ),
                
                # Przyciski akcji
                ft.Row([
                    ft.ElevatedButton(
                        "💾 Zapisz roboczy",
                        on_click=self._save_draft,
                        style=ft.ButtonStyle(bgcolor=ft.Colors.GREY_300)
                    ),
                    ft.ElevatedButton(
                        "✅ Zakończ protokół",
                        on_click=self._complete_protocol,
                        style=ft.ButtonStyle(bgcolor=ft.Colors.GREEN)
                    )
                ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
            ], spacing=10, scroll=ft.ScrollMode.AUTO, expand=True)
        ], spacing=10)
    
    def _create_profile_view(self) -> ft.Column:
        """Tworzy widok profilu technika"""
        return ft.Column([
            ft.Text("👤 Profil technika", size=20, weight=ft.FontWeight.BOLD),
            
            # Informacje o techniku
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.CircleAvatar(
                                content=ft.Text("TN", size=20),
                                radius=30,
                                bgcolor=ft.Colors.BLUE
                            ),
                            ft.Column([
                                ft.Text(self.current_technician['name'], 
                                       size=18, weight=ft.FontWeight.BOLD),
                                ft.Text(self.current_technician['certification']),
                                ft.Text(self.current_technician['phone'])
                            ], spacing=2)
                        ], spacing=15),
                        
                        ft.Divider(),
                        
                        # Statystyki
                        ft.Text("📊 Statystyki miesiąca", weight=ft.FontWeight.BOLD),
                        ft.Row([
                            self._create_stat_item("Zlecenia", "47", ft.Colors.BLUE),
                            self._create_stat_item("Ocena", "4.8", ft.Colors.GREEN),
                            self._create_stat_item("Protokoły", "45", ft.Colors.ORANGE),
                        ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
                    ], spacing=10),
                    padding=15
                )
            ),
            
            # Ustawienia
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text("⚙️ Ustawienia", weight=ft.FontWeight.BOLD),
                        
                        ft.Row([
                            ft.Text("GPS"),
                            ft.Switch(value=self.gps_enabled, on_change=self._toggle_gps)
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        
                        ft.Row([
                            ft.Text("Tryb offline"),
                            ft.Switch(value=self.offline_mode, on_change=self._toggle_offline)
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        
                        ft.ElevatedButton("🔄 Synchronizuj dane", on_click=self._sync_data),
                        ft.ElevatedButton("📤 Wyślij raporty", on_click=self._send_reports),
                    ], spacing=10),
                    padding=15
                )
            )
        ], spacing=10, scroll=ft.ScrollMode.AUTO)
    
    # Helper methods
    def _create_order_card(self, order: Dict) -> ft.Card:
        """Tworzy kartę zlecenia (kompaktową)"""
        return ft.Card(
            content=ft.Container(
                content=ft.Row([
                    ft.Column([
                        ft.Text(f"🏠 {order['customer']}", weight=ft.FontWeight.BOLD, size=12),
                        ft.Text(f"📍 {order['address']}", size=10),
                        ft.Text(f"⏰ {order['time']}", size=10, color=ft.Colors.BLUE)
                    ], spacing=2, expand=True),
                    ft.Column([
                        ft.Chip(
                            label=ft.Text(order['status'], size=10),
                            bgcolor=self._get_status_color(order['status'])
                        ),
                        ft.IconButton(
                            icon=ft.Icons.ARROW_FORWARD,
                            icon_size=16,
                            on_click=lambda e, o=order: self._select_order(o)
                        )
                    ], horizontal_alignment=ft.CrossAxisAlignment.END)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                padding=10
            )
        )
    
    def _create_detailed_order_card(self, order: Dict) -> ft.Card:
        """Tworzy szczegółową kartę zlecenia"""
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Text(f"🏠 {order['customer']}", weight=ft.FontWeight.BOLD),
                        ft.Chip(
                            label=ft.Text(order['status']),
                            bgcolor=self._get_status_color(order['status'])
                        )
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                    
                    ft.Text(f"📍 {order['address']}"),
                    ft.Text(f"🔧 {order['equipment']}"),
                    ft.Text(f"⏰ {order['time']} | 📞 {order['phone']}"),
                    
                    ft.Row([
                        ft.ElevatedButton(
                            "📍 Nawiguj",
                            on_click=lambda e, o=order: self._navigate_to_order(o),
                            style=ft.ButtonStyle(bgcolor=ft.Colors.BLUE_100)
                        ),
                        ft.ElevatedButton(
                            "📝 Protokół",
                            on_click=lambda e, o=order: self._start_protocol_for_order(o),
                            style=ft.ButtonStyle(bgcolor=ft.Colors.GREEN_100)
                        )
                    ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
                ], spacing=5),
                padding=10
            )
        )
    
    def _get_demo_service_orders(self) -> List[Dict]:
        """Zwraca demo dane zleceń serwisowych"""
        return [
            {
                "id": "SO001",
                "customer": "Jan Kowalski",
                "address": "ul. Marszałkowska 1, Warszawa",
                "equipment": "Klimatyzator Daikin FTXM35R",
                "time": "09:00",
                "phone": "+48 123 456 789",
                "status": "W trakcie",
                "type": "Serwis"
            },
            {
                "id": "SO002", 
                "customer": "Anna Nowak",
                "address": "ul. Nowy Świat 15, Warszawa",
                "equipment": "Pompa ciepła LG HU123",
                "time": "11:30",
                "phone": "+48 987 654 321",
                "status": "Zaplanowane",
                "type": "Instalacja"
            },
            {
                "id": "SO003",
                "customer": "Firma ABC Sp. z o.o.",
                "address": "ul. Jerozolimskie 100, Warszawa", 
                "equipment": "System VRV Daikin",
                "time": "14:00",
                "phone": "+48 555 666 777",
                "status": "Zaplanowane",
                "type": "Przegląd"
            }
        ]
    
    # Event handlers (będą implementowane)
    def _change_view(self, view: str):
        self.current_view = view
        self.page.update()
    
    def _sync_data(self, e=None):
        logger.info("Synchronizing data...")
    
    def _select_order(self, order: Dict):
        self.selected_service_order = order
        self.current_view = "protocol"
        self.page.update()
    
    # Pozostałe metody będą dodane w kolejnych iteracjach...
    def _get_status_color(self, status: str) -> str:
        colors = {
            "W trakcie": ft.Colors.ORANGE_100,
            "Zaplanowane": ft.Colors.BLUE_100,
            "Zakończone": ft.Colors.GREEN_100
        }
        return colors.get(status, ft.Colors.GREY_100)
