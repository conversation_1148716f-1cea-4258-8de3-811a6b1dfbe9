Metadata-Version: 2.4
Name: flet-hvac-interface
Version: 1.0.0
Summary: Advanced Flet-based interface for HVAC CRM system with AI integration
Home-page: https://github.com/hvac-crm/flet-interface
Author: HVAC CRM Team
Author-email: HVAC CRM Team <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/hvac-crm/flet-interface
Project-URL: Documentation, https://docs.hvaccrm.com
Project-URL: Repository, https://github.com/hvac-crm/flet-interface.git
Project-URL: Bug Tracker, https://github.com/hvac-crm/flet-interface/issues
Keywords: hvac,crm,flet,ui,interface,ai
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: User Interfaces
Classifier: Topic :: Office/Business :: Financial
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: flet<0.30.0,>=0.28.0
Requires-Dist: asyncio-mqtt>=0.16.0
Requires-Dist: pydantic>=2.0.0
Requires-Dist: httpx>=0.25.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: loguru>=0.7.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.5.0; extra == "dev"
Provides-Extra: docker
Requires-Dist: gunicorn>=21.0.0; extra == "docker"
Dynamic: author
Dynamic: home-page
Dynamic: requires-python

# 🔧 HVAC CRM Flet Interface

**Cutting-edge future-proof interface for comprehensive backend interaction**

## 🌟 Features

### 🚀 Core Capabilities
- **Real-time Multi-threading**: Advanced threading architecture for concurrent data processing
- **Material Design 3**: Modern, responsive UI with cosmic-level design principles
- **Backend Integration**: Seamless connection to GoSpine API, python_mixer, and databases
- **Live Updates**: Real-time data synchronization and UI updates
- **Multi-platform**: Desktop, web, and mobile deployment ready

### 📊 Interface Sections
1. **📈 Dashboard** - Real-time system overview and metrics
2. **👥 Customer Profiles** - 360° customer view with unified data
3. **📧 Email Intelligence** - AI-powered email analysis and processing
4. **📄 Document Processing** - OCR, invoice extraction, document analysis
5. **📅 Calendar Integration** - Intelligent scheduling and optimization
6. **📊 Analytics** - Business intelligence and insights
7. **⚙️ System Administration** - Configuration and monitoring

### 🧵 Threading Architecture
- **Main UI Thread** - Flet interface and user interactions
- **Email Monitor Thread** - IMAP <NAME_EMAIL> and <EMAIL>
- **Transcription Thread** - M4A file processing with NVIDIA NeMo
- **AI Analysis Thread** - Bielik V3/Gemma processing queue
- **Database Sync Thread** - MongoDB → PostgreSQL synchronization
- **Real-time Update Thread** - UI refresh coordination

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- UV package manager (recommended) or pip

### Installation & Launch
```bash
# Clone or navigate to the interface directory
cd /home/<USER>/HVAC/unifikacja/flet_hvac_interface

# Launch with auto-dependency installation
python launch.py

# Or manual installation
uv pip install -r requirements.txt
python main.py
```

## 🏗️ Architecture

### Backend Integrations
- **GoSpine API**: REST API integration for HVAC CRM operations
- **Python Mixer**: Direct integration for data processing
- **PostgreSQL**: Operational data storage
- **MongoDB**: Unstructured data (emails, transcriptions)
- **Redis**: Caching and real-time updates
- **MinIO**: File storage for attachments and documents

### AI Model Integration
- **LM Studio**: Local AI model hosting
- **Bielik V3**: Polish language processing
- **Gemma Models**: General AI analysis
- **NVIDIA NeMo**: Speech-to-text transcription

## 📁 Project Structure
```
flet_hvac_interface/
├── main.py                 # Main application entry point
├── launch.py              # Auto-launcher with dependency checking
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── core/                 # Core application modules
│   ├── config.py         # Configuration management
│   ├── threading_manager.py # Threading coordination
│   └── app.py           # Main application class
├── interfaces/          # UI interface modules
│   ├── dashboard.py     # Dashboard interface
│   ├── customer_profiles.py # Customer 360° view
│   ├── email_intelligence.py # Email processing UI
│   ├── document_processing.py # Document analysis UI
│   ├── calendar_manager.py # Calendar integration
│   └── analytics.py     # Analytics dashboard
├── services/           # Backend service clients
│   ├── gospine_client.py # GoSpine API client
│   ├── python_mixer_client.py # Python mixer integration
│   ├── database_manager.py # Multi-database operations
│   └── ai_service.py   # AI model integration
└── utils/             # Utility modules
    ├── real_time_updates.py # Live update system
    └── data_processors.py # Data transformation
```
